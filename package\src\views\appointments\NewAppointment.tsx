import { Icon } from "@iconify/react";
import { <PERSON> } from "react-router-dom";
import AppointmentForm from "../../components/appointments/AppointmentForm";

const NewAppointment = () => {
  return (
    <>
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-2">
            <h5 className="card-title">Nuovo Appuntamento</h5>
            <Link to="/appointments" className="text-gray-500 hover:text-primary">
              <Icon icon="solar:arrow-left-linear" height={20} />
              <span className="sr-only">Torna alla lista appuntamenti</span>
            </Link>
          </div>
        </div>

        <AppointmentForm />
      </div>
    </>
  );
};

export default NewAppointment;
