import { prismaService } from './PrismaService';
import { IAppointment, ICreateAppointment } from '../types/appointments/IAppointment';

export class AppointmentDatabaseService {
  static async getAllAppointments(): Promise<IAppointment[]> {
    const prisma = prismaService.getClient();
    const appointments = await prisma.appointment.findMany({
      include: { patient: true }
    });
    return appointments.map(a => ({
      ...a,
      status: a.status as import('../types/appointments/IAppointment').AppointmentStatus,
      description: a.description ?? undefined,
      notes: a.notes ?? undefined,
      type: a.type ?? undefined,
      patient: a.patient ? {
        id: a.patient.id,
        firstName: a.patient.firstName,
        lastName: a.patient.lastName,
        phone: a.patient.phone ?? undefined,
        email: a.patient.email ?? undefined
      } : undefined
    }));
  }

  static async getAppointmentById(id: string): Promise<IAppointment | null> {
    const prisma = prismaService.getClient();
    const a = await prisma.appointment.findUnique({
      where: { id },
      include: { patient: true }
    });
    if (!a) return null;
    return {
      ...a,
      status: a.status as import('../types/appointments/IAppointment').AppointmentStatus,
      description: a.description ?? undefined,
      notes: a.notes ?? undefined,
      type: a.type ?? undefined,
      patient: a.patient ? {
        id: a.patient.id,
        firstName: a.patient.firstName,
        lastName: a.patient.lastName,
        phone: a.patient.phone ?? undefined,
        email: a.patient.email ?? undefined
      } : undefined
    };
  }

  static async createAppointment(data: ICreateAppointment): Promise<IAppointment> {
    const prisma = prismaService.getClient();
    const a = await prisma.appointment.create({
      data,
      include: { patient: true }
    });
    return {
      ...a,
      status: a.status as import('../types/appointments/IAppointment').AppointmentStatus,
      description: a.description ?? undefined,
      notes: a.notes ?? undefined,
      type: a.type ?? undefined,
      patient: a.patient ? {
        id: a.patient.id,
        firstName: a.patient.firstName,
        lastName: a.patient.lastName,
        phone: a.patient.phone ?? undefined,
        email: a.patient.email ?? undefined
      } : undefined
    };
  }

  static async updateAppointment(id: string, data: Partial<ICreateAppointment>): Promise<IAppointment> {
    const prisma = prismaService.getClient();
    const a = await prisma.appointment.update({
      where: { id },
      data,
      include: { patient: true }
    });
    return {
      ...a,
      status: a.status as import('../types/appointments/IAppointment').AppointmentStatus,
      description: a.description ?? undefined,
      notes: a.notes ?? undefined,
      type: a.type ?? undefined,
      patient: a.patient ? {
        id: a.patient.id,
        firstName: a.patient.firstName,
        lastName: a.patient.lastName,
        phone: a.patient.phone ?? undefined,
        email: a.patient.email ?? undefined
      } : undefined
    };
  }

  static async deleteAppointment(id: string): Promise<boolean> {
    const prisma = prismaService.getClient();
    await prisma.appointment.delete({ where: { id } });
    return true;
  }
}
