/**
 * Servizio API per la comunicazione con il backend
 * 
 * Questo servizio gestisce tutte le chiamate HTTP al backend che utilizza Prisma.
 * Per ora implementa un mock dei dati per testare l'integrazione frontend,
 * ma può essere facilmente sostituito con chiamate HTTP reali.
 * 
 * Caratteristiche:
 * - Simulazione realistica delle operazioni CRUD
 * - Gestione errori e loading states
 * - Compatibilità con l'interfaccia Prisma
 * - Facile migrazione a API reali
 */

import { IPatient, ICreatePatient } from '../types/patients/IPatient';

/**
 * Dati mock per simulare il database
 * Questi dati verranno sostituiti con chiamate HTTP reali
 */
let mockPatients: IPatient[] = [
  {
    id: 'clx1a2b3c4d5e6f7g8h9i0j1',
    firstName: 'Mario',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+39 ***********',
    dateOfBirth: '1980-05-15',
    address: 'Via Roma 123',
    city: 'Milano',
    postalCode: '20100',
    fiscalCode: '****************',
    notes: 'Paziente con allergia al lattice',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: 'clx2b3c4d5e6f7g8h9i0j1k2',
    firstName: 'Giulia',
    lastName: 'Bianchi',
    email: '<EMAIL>',
    phone: '+39 ***********',
    dateOfBirth: '1992-08-22',
    address: 'Corso Italia 456',
    city: 'Roma',
    postalCode: '00100',
    fiscalCode: '****************',
    notes: null,
    createdAt: '2024-01-16T14:30:00Z',
    updatedAt: '2024-01-16T14:30:00Z',
  },
  {
    id: 'clx3c4d5e6f7g8h9i0j1k2l3',
    firstName: 'Luca',
    lastName: 'Verdi',
    email: '<EMAIL>',
    phone: '+39 ***********',
    dateOfBirth: '1975-12-03',
    address: 'Piazza Duomo 789',
    city: 'Firenze',
    postalCode: '50100',
    fiscalCode: '****************',
    notes: 'Controllo semestrale',
    createdAt: '2024-01-17T09:15:00Z',
    updatedAt: '2024-01-17T09:15:00Z',
  },
];

/**
 * Simula un delay di rete per rendere più realistica la simulazione
 */
const simulateNetworkDelay = (ms: number = 500): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Genera un ID univoco per i nuovi record
 */
const generateId = (): string => {
  return 'clx' + Math.random().toString(36).substr(2, 21);
};

/**
 * Servizio API per la gestione dei pazienti
 */
export class ApiService {
  /**
   * Ottiene tutti i pazienti
   * @param filters Filtri di ricerca (opzionali)
   * @param page Numero pagina
   * @param limit Elementi per pagina
   * @returns Promise con lista pazienti paginata
   */
  static async getPatients(
    filters: any = {},
    page: number = 1,
    limit: number = 20
  ): Promise<{ patients: IPatient[]; total: number; pages: number }> {
    await simulateNetworkDelay();
    
    try {
      let filteredPatients = [...mockPatients];
      
      // Applica filtri se presenti
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        filteredPatients = filteredPatients.filter(patient => 
          patient.firstName.toLowerCase().includes(searchTerm) ||
          patient.lastName.toLowerCase().includes(searchTerm) ||
          (patient.email && patient.email.toLowerCase().includes(searchTerm))
        );
      }
      
      if (filters.city) {
        filteredPatients = filteredPatients.filter(patient => 
          patient.city && patient.city.toLowerCase().includes(filters.city.toLowerCase())
        );
      }
      
      // Paginazione
      const total = filteredPatients.length;
      const pages = Math.ceil(total / limit);
      const skip = (page - 1) * limit;
      const patients = filteredPatients.slice(skip, skip + limit);
      
      console.log(`✅ API Mock: Recuperati ${patients.length} pazienti (pagina ${page}/${pages})`);
      return { patients, total, pages };
    } catch (error) {
      console.error('❌ API Mock: Errore recupero pazienti:', error);
      throw new Error('Errore durante il recupero dei pazienti');
    }
  }

  /**
   * Ottiene un paziente per ID
   * @param id ID del paziente
   * @returns Promise con il paziente o null
   */
  static async getPatientById(id: string): Promise<IPatient | null> {
    await simulateNetworkDelay(300);
    
    try {
      const patient = mockPatients.find(p => p.id === id) || null;
      
      if (patient) {
        console.log('✅ API Mock: Paziente trovato:', patient.id);
      } else {
        console.log('⚠️ API Mock: Paziente non trovato:', id);
      }
      
      return patient;
    } catch (error) {
      console.error('❌ API Mock: Errore recupero paziente:', error);
      throw new Error('Errore durante il recupero del paziente');
    }
  }

  /**
   * Crea un nuovo paziente
   * @param data Dati del paziente da creare
   * @returns Promise con il paziente creato
   */
  static async createPatient(data: ICreatePatient): Promise<IPatient> {
    await simulateNetworkDelay(800);
    
    try {
      // Validazioni
      if (data.email && mockPatients.some(p => p.email === data.email)) {
        throw new Error('Email già esistente nel sistema');
      }
      
      if (data.fiscalCode && mockPatients.some(p => p.fiscalCode === data.fiscalCode)) {
        throw new Error('Codice fiscale già esistente nel sistema');
      }
      
      // Crea il nuovo paziente
      const newPatient: IPatient = {
        id: generateId(),
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email || null,
        phone: data.phone || null,
        dateOfBirth: data.dateOfBirth || null,
        address: data.address || null,
        city: data.city || null,
        postalCode: data.postalCode || null,
        fiscalCode: data.fiscalCode || null,
        notes: data.notes || null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      // Aggiunge alla lista mock
      mockPatients.push(newPatient);
      
      console.log('✅ API Mock: Paziente creato con successo:', newPatient.id);
      return newPatient;
    } catch (error) {
      console.error('❌ API Mock: Errore creazione paziente:', error);
      throw error;
    }
  }

  /**
   * Aggiorna un paziente esistente
   * @param id ID del paziente da aggiornare
   * @param data Dati da aggiornare
   * @returns Promise con il paziente aggiornato
   */
  static async updatePatient(id: string, data: Partial<ICreatePatient>): Promise<IPatient> {
    await simulateNetworkDelay(600);
    
    try {
      const patientIndex = mockPatients.findIndex(p => p.id === id);
      
      if (patientIndex === -1) {
        throw new Error('Paziente non trovato');
      }
      
      // Validazioni per campi unici
      if (data.email && mockPatients.some(p => p.email === data.email && p.id !== id)) {
        throw new Error('Email già esistente nel sistema');
      }
      
      if (data.fiscalCode && mockPatients.some(p => p.fiscalCode === data.fiscalCode && p.id !== id)) {
        throw new Error('Codice fiscale già esistente nel sistema');
      }
      
      // Aggiorna il paziente
      const updatedPatient: IPatient = {
        ...mockPatients[patientIndex],
        ...data,
        updatedAt: new Date().toISOString(),
      };
      
      mockPatients[patientIndex] = updatedPatient;
      
      console.log('✅ API Mock: Paziente aggiornato con successo:', updatedPatient.id);
      return updatedPatient;
    } catch (error) {
      console.error('❌ API Mock: Errore aggiornamento paziente:', error);
      throw error;
    }
  }

  /**
   * Elimina un paziente
   * @param id ID del paziente da eliminare
   * @returns Promise con true se eliminato con successo
   */
  static async deletePatient(id: string): Promise<boolean> {
    await simulateNetworkDelay(400);
    
    try {
      const patientIndex = mockPatients.findIndex(p => p.id === id);
      
      if (patientIndex === -1) {
        throw new Error('Paziente non trovato');
      }
      
      // Rimuove il paziente dalla lista
      mockPatients.splice(patientIndex, 1);
      
      console.log('✅ API Mock: Paziente eliminato con successo:', id);
      return true;
    } catch (error) {
      console.error('❌ API Mock: Errore eliminazione paziente:', error);
      throw error;
    }
  }

  /**
   * Cerca pazienti per query
   * @param query Stringa di ricerca
   * @param limit Numero massimo risultati
   * @returns Promise con array di pazienti trovati
   */
  static async searchPatients(query: string, limit: number = 10): Promise<IPatient[]> {
    await simulateNetworkDelay(300);
    
    try {
      const searchTerm = query.toLowerCase();
      const results = mockPatients
        .filter(patient => 
          patient.firstName.toLowerCase().includes(searchTerm) ||
          patient.lastName.toLowerCase().includes(searchTerm) ||
          (patient.email && patient.email.toLowerCase().includes(searchTerm))
        )
        .slice(0, limit);
      
      console.log(`✅ API Mock: Trovati ${results.length} pazienti per query: "${query}"`);
      return results;
    } catch (error) {
      console.error('❌ API Mock: Errore ricerca pazienti:', error);
      throw new Error('Errore durante la ricerca dei pazienti');
    }
  }
}

export default ApiService;
