/**
 * Pagina per testare la connessione al database
 */

import PageContainer from 'src/components/container/PageContainer';
import DatabaseTestComponent from 'src/components/database/DatabaseTestComponent';

const DatabaseTest = () => {
  return (
    <PageContainer title="Test Database" description="Verifica connessione e operazioni database">
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Test Database Neon
          </h1>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Questa pagina permette di verificare che la connessione al database Neon funzioni correttamente
            e che tutte le operazioni CRUD siano operative prima di integrare i form.
          </p>
        </div>

        <DatabaseTestComponent />
      </div>
    </PageContainer>
  );
};

export default DatabaseTest;
