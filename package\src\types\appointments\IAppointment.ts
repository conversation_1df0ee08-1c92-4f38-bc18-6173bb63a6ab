/**
 * Tipi e interfacce per il sistema di gestione appuntamenti
 * Compatibili con lo schema Prisma e il database PostgreSQL
 */

// Enum per lo stato degli appuntamenti (deve corrispondere al Prisma schema)
export enum AppointmentStatus {
  SCHEDULED = 'SCHEDULED',
  CONFIRMED = 'CONFIRMED', 
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  NO_SHOW = 'NO_SHOW'
}

// Interfaccia principale per un appuntamento
export interface IAppointment {
  id: string;
  patientId: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  status: AppointmentStatus;
  type?: string; // Tipo di trattamento
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Relazioni (opzionali per il popolamento)
  patient?: {
    id: string;
    firstName: string;
    lastName: string;
    phone?: string;
    email?: string;
  };
}

// Interfaccia per la creazione di un nuovo appuntamento
export interface ICreateAppointment {
  patientId: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  status?: AppointmentStatus;
  type?: string;
  notes?: string;
}

// Interfaccia per l'aggiornamento di un appuntamento
export interface IUpdateAppointment {
  patientId?: string;
  title?: string;
  description?: string;
  startTime?: Date;
  endTime?: Date;
  status?: AppointmentStatus;
  type?: string;
  notes?: string;
}

// Interfaccia per i filtri di ricerca appuntamenti
export interface IAppointmentFilters {
  patientId?: string;
  status?: AppointmentStatus;
  type?: string;
  startDate?: Date;
  endDate?: Date;
  searchTerm?: string;
}

// Interfaccia per le statistiche degli appuntamenti
export interface IAppointmentStats {
  total: number;
  scheduled: number;
  confirmed: number;
  inProgress: number;
  completed: number;
  cancelled: number;
  noShow: number;
  todayTotal: number;
  weekTotal: number;
  monthTotal: number;
}

// Interfaccia per i dati del form appuntamento
export interface IAppointmentFormData {
  patientId: string;
  title: string;
  description: string;
  date: string; // formato YYYY-MM-DD
  startTime: string; // formato HH:MM
  endTime: string; // formato HH:MM
  status: AppointmentStatus;
  type: string;
  notes: string;
}

// Tipi di trattamento predefiniti
export const TREATMENT_TYPES = [
  'Visita di controllo',
  'Pulizia dentale',
  'Otturazione',
  'Estrazione',
  'Devitalizzazione',
  'Impianto',
  'Ortodonzia',
  'Sbiancamento',
  'Protesi',
  'Chirurgia orale',
  'Parodontologia',
  'Endodonzia',
  'Altro'
] as const;

export type TreatmentType = typeof TREATMENT_TYPES[number];

// Utility per la conversione dello stato in italiano
export const getStatusLabel = (status: AppointmentStatus): string => {
  const labels: Record<AppointmentStatus, string> = {
    [AppointmentStatus.SCHEDULED]: 'Programmato',
    [AppointmentStatus.CONFIRMED]: 'Confermato',
    [AppointmentStatus.IN_PROGRESS]: 'In corso',
    [AppointmentStatus.COMPLETED]: 'Completato',
    [AppointmentStatus.CANCELLED]: 'Cancellato',
    [AppointmentStatus.NO_SHOW]: 'Non presentato'
  };
  return labels[status];
};

// Utility per il colore del badge dello stato
export const getStatusColor = (status: AppointmentStatus): string => {
  const colors: Record<AppointmentStatus, string> = {
    [AppointmentStatus.SCHEDULED]: 'info',
    [AppointmentStatus.CONFIRMED]: 'success',
    [AppointmentStatus.IN_PROGRESS]: 'warning',
    [AppointmentStatus.COMPLETED]: 'success',
    [AppointmentStatus.CANCELLED]: 'failure',
    [AppointmentStatus.NO_SHOW]: 'failure'
  };
  return colors[status];
};

// Utility per formattare data e ora
export const formatAppointmentDateTime = (date: Date): string => {
  return new Intl.DateTimeFormat('it-IT', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

export const formatAppointmentDate = (date: Date): string => {
  return new Intl.DateTimeFormat('it-IT', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
};

export const formatAppointmentTime = (date: Date): string => {
  return new Intl.DateTimeFormat('it-IT', {
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};
