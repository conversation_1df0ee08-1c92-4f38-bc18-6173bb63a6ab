import { prismaService } from './PrismaService';
import { IInvoice, ICreateInvoice } from '../types/invoices/IInvoice';

export class InvoiceDatabaseService {
  static async getAllInvoices(): Promise<IInvoice[]> {
    const prisma = prismaService.getClient();
    const invoices = await prisma.invoice.findMany({
      include: { patient: true }
    });
    return invoices.map(i => ({
      ...i,
      status: i.status as import('../types/invoices/IInvoice').InvoiceStatus,
      dueDate: i.dueDate ?? undefined,
      taxAmount: i.taxAmount !== null && i.taxAmount !== undefined ? Number(i.taxAmount) : undefined,
      amount: Number(i.amount),
      totalAmount: Number(i.totalAmount),
      description: i.description ?? undefined,
      notes: i.notes ?? undefined,
      patient: i.patient ? {
        id: i.patient.id,
        firstName: i.patient.firstName,
        lastName: i.patient.lastName,
        phone: i.patient.phone ?? undefined,
        email: i.patient.email ?? undefined,
        fiscalCode: i.patient.fiscalCode ?? undefined
      } : undefined
    }));
  }

  static async getInvoiceById(id: string): Promise<IInvoice | null> {
    const prisma = prismaService.getClient();
    const i = await prisma.invoice.findUnique({
      where: { id },
      include: { patient: true }
    });
    if (!i) return null;
    return {
      ...i,
      status: i.status as import('../types/invoices/IInvoice').InvoiceStatus,
      dueDate: i.dueDate ?? undefined,
      taxAmount: i.taxAmount !== null && i.taxAmount !== undefined ? Number(i.taxAmount) : undefined,
      amount: Number(i.amount),
      totalAmount: Number(i.totalAmount),
      description: i.description ?? undefined,
      notes: i.notes ?? undefined,
      patient: i.patient ? {
        id: i.patient.id,
        firstName: i.patient.firstName,
        lastName: i.patient.lastName,
        phone: i.patient.phone ?? undefined,
        email: i.patient.email ?? undefined,
        fiscalCode: i.patient.fiscalCode ?? undefined
      } : undefined
    };
  }

  static async createInvoice(data: ICreateInvoice): Promise<IInvoice> {
    const prisma = prismaService.getClient();
    // Prisma richiede tutti i campi obbligatori, calcola totalAmount e invoiceNumber se non presenti
    const invoiceData = {
      ...data,
      invoiceNumber: data.invoiceNumber ?? `INV-${Date.now()}`,
      totalAmount: data.amount + (data.taxAmount ?? 0)
    };
    const i = await prisma.invoice.create({
      data: invoiceData,
      include: { patient: true }
    });
    return {
      ...i,
      status: i.status as import('../types/invoices/IInvoice').InvoiceStatus,
      dueDate: i.dueDate ?? undefined,
      taxAmount: i.taxAmount !== null && i.taxAmount !== undefined ? Number(i.taxAmount) : undefined,
      amount: Number(i.amount),
      totalAmount: Number(i.totalAmount),
      description: i.description ?? undefined,
      notes: i.notes ?? undefined,
      patient: i.patient ? {
        id: i.patient.id,
        firstName: i.patient.firstName,
        lastName: i.patient.lastName,
        phone: i.patient.phone ?? undefined,
        email: i.patient.email ?? undefined,
        fiscalCode: i.patient.fiscalCode ?? undefined
      } : undefined
    };
  }

  static async updateInvoice(id: string, data: Partial<ICreateInvoice>): Promise<IInvoice> {
    const prisma = prismaService.getClient();
    // Calcola totalAmount solo se amount e taxAmount sono presenti
    let totalAmount: number | undefined = undefined;
    if (typeof data.amount === 'number') {
      totalAmount = data.amount + (data.taxAmount ?? 0);
    }
    const updateData = {
      ...data,
      totalAmount
    };
    const i = await prisma.invoice.update({
      where: { id },
      data: updateData,
      include: { patient: true }
    });
    return {
      ...i,
      status: i.status as import('../types/invoices/IInvoice').InvoiceStatus,
      dueDate: i.dueDate ?? undefined,
      taxAmount: i.taxAmount !== null && i.taxAmount !== undefined ? Number(i.taxAmount) : undefined,
      amount: Number(i.amount),
      totalAmount: Number(i.totalAmount),
      description: i.description ?? undefined,
      notes: i.notes ?? undefined,
      patient: i.patient ? {
        id: i.patient.id,
        firstName: i.patient.firstName,
        lastName: i.patient.lastName,
        phone: i.patient.phone ?? undefined,
        email: i.patient.email ?? undefined,
        fiscalCode: i.patient.fiscalCode ?? undefined
      } : undefined
    };
  }

  static async deleteInvoice(id: string): Promise<boolean> {
    const prisma = prismaService.getClient();
    await prisma.invoice.delete({ where: { id } });
    return true;
  }
}
