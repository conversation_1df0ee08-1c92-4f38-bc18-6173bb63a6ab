/**
 * Servizio per le operazioni database sui pazienti
 * 
 * Questo servizio gestisce tutte le operazioni CRUD sui pazienti
 * utilizzando Prisma Client. È progettato per essere utilizzato
 * negli hook React per fornire dati reali dal database.
 */

import { prismaService } from './PrismaService';
import { IPatient, ICreatePatient } from '../types/patients/IPatient';

export class PatientDatabaseService {
  /**
   * Ottiene tutti i pazienti dal database
   */
  static async getAllPatients(): Promise<IPatient[]> {
    try {
      const prisma = prismaService.getClient();
      
      const patients = await prisma.patient.findMany({
        orderBy: [
          { lastName: 'asc' },
          { firstName: 'asc' }
        ]
      });

      // Converte i dati Prisma nel formato IPatient
      return patients.map(patient => ({
        id: patient.id,
        firstName: patient.firstName,
        lastName: patient.lastName,
        email: patient.email,
        phone: patient.phone,
        dateOfBirth: patient.dateOfBirth,
        address: patient.address,
        city: patient.city,
        postalCode: patient.postalCode,
        fiscalCode: patient.fiscalCode,
        notes: patient.notes,
        createdAt: patient.createdAt,
        updatedAt: patient.updatedAt
      }));

    } catch (error) {
      console.error('❌ Errore caricamento pazienti:', error);
      throw new Error('Errore durante il caricamento dei pazienti');
    }
  }

  /**
   * Ottiene un paziente per ID
   */
  static async getPatientById(id: string): Promise<IPatient | null> {
    try {
      const prisma = prismaService.getClient();
      
      const patient = await prisma.patient.findUnique({
        where: { id },
        include: {
          appointments: {
            orderBy: { startTime: 'desc' },
            take: 5 // Ultimi 5 appuntamenti
          },
          invoices: {
            orderBy: { issueDate: 'desc' },
            take: 5 // Ultime 5 fatture
          },
          files: {
            orderBy: { createdAt: 'desc' }
          }
        }
      });

      if (!patient) {
        return null;
      }

      return {
        id: patient.id,
        firstName: patient.firstName,
        lastName: patient.lastName,
        email: patient.email,
        phone: patient.phone,
        dateOfBirth: patient.dateOfBirth,
        address: patient.address,
        city: patient.city,
        postalCode: patient.postalCode,
        fiscalCode: patient.fiscalCode,
        notes: patient.notes,
        createdAt: patient.createdAt,
        updatedAt: patient.updatedAt
      };

    } catch (error) {
      console.error('❌ Errore recupero paziente:', error);
      throw new Error('Errore durante il recupero del paziente');
    }
  }

  /**
   * Crea un nuovo paziente
   */
  static async createPatient(patientData: ICreatePatient): Promise<IPatient> {
    try {
      const prisma = prismaService.getClient();
      
      const patient = await prisma.patient.create({
        data: {
          firstName: patientData.firstName,
          lastName: patientData.lastName,
          email: patientData.email,
          phone: patientData.phone,
          dateOfBirth: patientData.dateOfBirth,
          address: patientData.address,
          city: patientData.city,
          postalCode: patientData.postalCode,
          fiscalCode: patientData.fiscalCode,
          notes: patientData.notes
        }
      });

      return {
        id: patient.id,
        firstName: patient.firstName,
        lastName: patient.lastName,
        email: patient.email,
        phone: patient.phone,
        dateOfBirth: patient.dateOfBirth,
        address: patient.address,
        city: patient.city,
        postalCode: patient.postalCode,
        fiscalCode: patient.fiscalCode,
        notes: patient.notes,
        createdAt: patient.createdAt,
        updatedAt: patient.updatedAt
      };

    } catch (error) {
      console.error('❌ Errore creazione paziente:', error);
      
      // Gestione errori specifici di Prisma
      if (error.code === 'P2002') {
        throw new Error('Email o codice fiscale già esistente');
      }
      
      throw new Error('Errore durante la creazione del paziente');
    }
  }

  /**
   * Aggiorna un paziente esistente
   */
  static async updatePatient(id: string, patientData: Partial<ICreatePatient>): Promise<IPatient> {
    try {
      const prisma = prismaService.getClient();
      
      const patient = await prisma.patient.update({
        where: { id },
        data: {
          ...(patientData.firstName && { firstName: patientData.firstName }),
          ...(patientData.lastName && { lastName: patientData.lastName }),
          ...(patientData.email && { email: patientData.email }),
          ...(patientData.phone && { phone: patientData.phone }),
          ...(patientData.dateOfBirth && { dateOfBirth: patientData.dateOfBirth }),
          ...(patientData.address && { address: patientData.address }),
          ...(patientData.city && { city: patientData.city }),
          ...(patientData.postalCode && { postalCode: patientData.postalCode }),
          ...(patientData.fiscalCode && { fiscalCode: patientData.fiscalCode }),
          ...(patientData.notes !== undefined && { notes: patientData.notes })
        }
      });

      return {
        id: patient.id,
        firstName: patient.firstName,
        lastName: patient.lastName,
        email: patient.email,
        phone: patient.phone,
        dateOfBirth: patient.dateOfBirth,
        address: patient.address,
        city: patient.city,
        postalCode: patient.postalCode,
        fiscalCode: patient.fiscalCode,
        notes: patient.notes,
        createdAt: patient.createdAt,
        updatedAt: patient.updatedAt
      };

    } catch (error) {
      console.error('❌ Errore aggiornamento paziente:', error);
      
      if (error.code === 'P2025') {
        throw new Error('Paziente non trovato');
      } else if (error.code === 'P2002') {
        throw new Error('Email o codice fiscale già esistente');
      }
      
      throw new Error('Errore durante l\'aggiornamento del paziente');
    }
  }

  /**
   * Elimina un paziente
   */
  static async deletePatient(id: string): Promise<boolean> {
    try {
      const prisma = prismaService.getClient();
      
      await prisma.patient.delete({
        where: { id }
      });

      return true;

    } catch (error) {
      console.error('❌ Errore eliminazione paziente:', error);
      
      if (error.code === 'P2025') {
        throw new Error('Paziente non trovato');
      }
      
      throw new Error('Errore durante l\'eliminazione del paziente');
    }
  }

  /**
   * Cerca pazienti per query
   */
  static async searchPatients(query: string): Promise<IPatient[]> {
    try {
      const prisma = prismaService.getClient();
      
      const patients = await prisma.patient.findMany({
        where: {
          OR: [
            { firstName: { contains: query, mode: 'insensitive' } },
            { lastName: { contains: query, mode: 'insensitive' } },
            { email: { contains: query, mode: 'insensitive' } },
            { phone: { contains: query } },
            { fiscalCode: { contains: query, mode: 'insensitive' } }
          ]
        },
        orderBy: [
          { lastName: 'asc' },
          { firstName: 'asc' }
        ]
      });

      return patients.map(patient => ({
        id: patient.id,
        firstName: patient.firstName,
        lastName: patient.lastName,
        email: patient.email,
        phone: patient.phone,
        dateOfBirth: patient.dateOfBirth,
        address: patient.address,
        city: patient.city,
        postalCode: patient.postalCode,
        fiscalCode: patient.fiscalCode,
        notes: patient.notes,
        createdAt: patient.createdAt,
        updatedAt: patient.updatedAt
      }));

    } catch (error) {
      console.error('❌ Errore ricerca pazienti:', error);
      throw new Error('Errore durante la ricerca dei pazienti');
    }
  }

  /**
   * Conta il numero totale di pazienti
   */
  static async getPatientCount(): Promise<number> {
    try {
      const prisma = prismaService.getClient();
      return await prisma.patient.count();
    } catch (error) {
      console.error('❌ Errore conteggio pazienti:', error);
      return 0;
    }
  }
}
