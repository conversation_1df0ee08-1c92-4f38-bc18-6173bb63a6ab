/**
 * Tipi e interfacce per il sistema di gestione fatture
 * Compatibili con lo schema Prisma e il database PostgreSQL
 */

// Enum per lo stato delle fatture (deve corrispondere al Prisma schema)
export enum InvoiceStatus {
  DRAFT = 'DRAFT',
  SENT = 'SENT',
  PAID = 'PAID',
  OVERDUE = 'OVERDUE',
  CANCELLED = 'CANCELLED'
}

// Interfaccia principale per una fattura
export interface IInvoice {
  id: string;
  patientId: string;
  invoiceNumber: string;
  issueDate: Date;
  dueDate?: Date;
  amount: number;
  taxAmount?: number;
  totalAmount: number;
  status: InvoiceStatus;
  description?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Relazioni (opzionali per il popolamento)
  patient?: {
    id: string;
    firstName: string;
    lastName: string;
    fiscalCode?: string;
    email?: string;
    phone?: string;
  };
}

// Interfaccia per la creazione di una nuova fattura
export interface ICreateInvoice {
  patientId: string;
  invoiceNumber?: string; // Se non fornito, verrà generato automaticamente
  issueDate: Date;
  dueDate?: Date;
  amount: number;
  taxAmount?: number;
  description?: string;
  notes?: string;
  status?: InvoiceStatus;
}

// Interfaccia per l'aggiornamento di una fattura
export interface IUpdateInvoice {
  patientId?: string;
  invoiceNumber?: string;
  issueDate?: Date;
  dueDate?: Date;
  amount?: number;
  taxAmount?: number;
  description?: string;
  notes?: string;
  status?: InvoiceStatus;
}

// Interfaccia per i filtri di ricerca fatture
export interface IInvoiceFilters {
  patientId?: string;
  status?: InvoiceStatus;
  startDate?: Date;
  endDate?: Date;
  minAmount?: number;
  maxAmount?: number;
  searchTerm?: string;
}

// Interfaccia per le statistiche delle fatture
export interface IInvoiceStats {
  total: number;
  draft: number;
  sent: number;
  paid: number;
  overdue: number;
  cancelled: number;
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
  overdueAmount: number;
  monthlyTotal: number;
  yearlyTotal: number;
}

// Interfaccia per i dati del form fattura
export interface IInvoiceFormData {
  patientId: string;
  invoiceNumber: string;
  issueDate: string; // formato YYYY-MM-DD
  dueDate: string; // formato YYYY-MM-DD
  amount: string;
  taxRate: string; // percentuale IVA
  taxAmount: string;
  totalAmount: string;
  status: InvoiceStatus;
  description: string;
  notes: string;
}

// Interfaccia per gli elementi di una fattura (per future estensioni)
export interface IInvoiceItem {
  id: string;
  invoiceId: string;
  description: string;
  quantity: number;
  unitPrice: number;
  taxRate: number;
  taxAmount: number;
  totalAmount: number;
}

// Utility per la conversione dello stato in italiano
export const getStatusLabel = (status: InvoiceStatus): string => {
  const labels: Record<InvoiceStatus, string> = {
    [InvoiceStatus.DRAFT]: 'Bozza',
    [InvoiceStatus.SENT]: 'Inviata',
    [InvoiceStatus.PAID]: 'Pagata',
    [InvoiceStatus.OVERDUE]: 'Scaduta',
    [InvoiceStatus.CANCELLED]: 'Cancellata'
  };
  return labels[status];
};

// Utility per il colore del badge dello stato
export const getStatusColor = (status: InvoiceStatus): string => {
  const colors: Record<InvoiceStatus, string> = {
    [InvoiceStatus.DRAFT]: 'gray',
    [InvoiceStatus.SENT]: 'info',
    [InvoiceStatus.PAID]: 'success',
    [InvoiceStatus.OVERDUE]: 'failure',
    [InvoiceStatus.CANCELLED]: 'failure'
  };
  return colors[status];
};

// Utility per formattare importi
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('it-IT', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount);
};

// Utility per formattare date
export const formatInvoiceDate = (date: Date): string => {
  return new Intl.DateTimeFormat('it-IT', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
};

// Utility per calcolare l'importo totale con IVA
export const calculateTotalWithTax = (amount: number, taxRate: number = 22): { taxAmount: number; totalAmount: number } => {
  const taxAmount = (amount * taxRate) / 100;
  const totalAmount = amount + taxAmount;
  
  return {
    taxAmount: Math.round(taxAmount * 100) / 100,
    totalAmount: Math.round(totalAmount * 100) / 100
  };
};

// Utility per generare numero fattura
export const generateInvoiceNumber = (year?: number): string => {
  const currentYear = year || new Date().getFullYear();
  const timestamp = Date.now().toString().slice(-6);
  return `FAT-${currentYear}-${timestamp}`;
};

// Utility per verificare se una fattura è scaduta
export const isInvoiceOverdue = (invoice: IInvoice): boolean => {
  if (!invoice.dueDate || invoice.status === InvoiceStatus.PAID || invoice.status === InvoiceStatus.CANCELLED) {
    return false;
  }
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const dueDate = new Date(invoice.dueDate);
  dueDate.setHours(0, 0, 0, 0);
  
  return dueDate < today;
};

// Utility per calcolare i giorni di ritardo
export const getDaysOverdue = (invoice: IInvoice): number => {
  if (!isInvoiceOverdue(invoice)) {
    return 0;
  }
  
  const today = new Date();
  const dueDate = new Date(invoice.dueDate!);
  const diffTime = today.getTime() - dueDate.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
};

// Costanti per aliquote IVA comuni
export const TAX_RATES = [
  { value: 0, label: '0% - Esente' },
  { value: 4, label: '4% - Ridotta' },
  { value: 10, label: '10% - Ridotta' },
  { value: 22, label: '22% - Ordinaria' }
] as const;

export type TaxRate = typeof TAX_RATES[number]['value'];
