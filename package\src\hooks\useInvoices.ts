/**
 * Hook personalizzato per la gestione delle fatture
 * Fornisce funzionalità CRUD complete con gestione stato e errori
 * Utilizza il nuovo servizio API che gestisce automaticamente l'ambiente
 */

import { useState, useEffect, useCallback } from 'react';
import {
  IInvoice,
  ICreateInvoice,
  IUpdateInvoice,
  IInvoiceFilters,
  IInvoiceStats,
  InvoiceStatus,
  getStatusLabel,
  getStatusColor,
  formatCurrency,
  formatInvoiceDate,
  generateInvoiceNumber,
  calculateTotalWithTax,
  isInvoiceOverdue
} from '../types/invoices/IInvoice';
import { createInvoice, getAllInvoices } from '../services/InvoiceApiService';

// Rimosso mock data - ora utilizziamo InvoiceApiService

interface UseInvoicesOptions {
  autoLoad?: boolean;
  filters?: IInvoiceFilters;
}

export const useInvoices = (options: UseInvoicesOptions = {}) => {
  const { autoLoad = true, filters } = options;
  
  // Stato locale
  const [invoices, setInvoices] = useState<IInvoice[]>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<IInvoice[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Carica tutte le fatture
  const loadInvoices = useCallback(async (): Promise<IInvoice[]> => {
    setIsLoading(true);
    setError(null);

    try {
      const invoicesData = await getAllInvoices();
      setInvoices(invoicesData);
      console.log('✅ Fatture caricate:', invoicesData.length);
      return invoicesData;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nel caricamento delle fatture';
      setError(errorMessage);
      console.error('❌ Errore caricamento fatture:', err);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Crea una nuova fattura
  const createInvoiceHandler = useCallback(async (invoiceData: ICreateInvoice): Promise<IInvoice | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const newInvoice = await createInvoice(invoiceData);

      // Aggiorna lo stato locale
      setInvoices(prev => [newInvoice, ...prev]);

      console.log('✅ Fattura creata:', newInvoice.id);
      return newInvoice;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nella creazione della fattura';
      setError(errorMessage);
      console.error('❌ Errore creazione fattura:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Aggiorna una fattura esistente
  const updateInvoice = useCallback(async (id: string, invoiceData: Partial<ICreateInvoice>): Promise<IInvoice | null> => {
    setIsLoading(true);
    setError(null);

    try {
      // TODO: Implementare updateInvoice API
      throw new Error('updateInvoice non ancora implementato');

      // Aggiorna lo stato locale
      setInvoices(prev => prev.map(invoice =>
        invoice.id === id ? updatedInvoice : invoice
      ));

      console.log('✅ Fattura aggiornata:', updatedInvoice.id);
      return updatedInvoice;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nell\'aggiornamento della fattura';
      setError(errorMessage);
      console.error('❌ Errore aggiornamento fattura:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Elimina una fattura
  const deleteInvoice = useCallback(async (id: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      // TODO: Implementare deleteInvoice API
      throw new Error('deleteInvoice non ancora implementato');

      if (result) {
        // Rimuovi dalla lista locale
        setInvoices(prev => prev.filter(invoice => invoice.id !== id));
        console.log('✅ Fattura eliminata:', id);
      }

      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nell\'eliminazione della fattura';
      setError(errorMessage);
      console.error('❌ Errore eliminazione fattura:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Pulisce l'errore corrente
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Caricamento iniziale
  useEffect(() => {
    if (autoLoad && invoices.length === 0) {
      loadInvoices();
    }
  }, [autoLoad, invoices.length, loadInvoices]);

  // Applicazione filtri
  useEffect(() => {
    let filtered = [...invoices];
    
    if (filters) {
      if (filters.patientId) {
        filtered = filtered.filter(inv => inv.patientId === filters.patientId);
      }
      
      if (filters.status) {
        filtered = filtered.filter(inv => inv.status === filters.status);
      }
      
      if (filters.startDate) {
        filtered = filtered.filter(inv => inv.issueDate >= filters.startDate!);
      }
      
      if (filters.endDate) {
        filtered = filtered.filter(inv => inv.issueDate <= filters.endDate!);
      }
      
      if (filters.minAmount) {
        filtered = filtered.filter(inv => inv.totalAmount >= filters.minAmount!);
      }
      
      if (filters.maxAmount) {
        filtered = filtered.filter(inv => inv.totalAmount <= filters.maxAmount!);
      }
      
      if (filters.searchTerm) {
        const term = filters.searchTerm.toLowerCase();
        filtered = filtered.filter(inv => 
          inv.invoiceNumber.toLowerCase().includes(term) ||
          inv.description?.toLowerCase().includes(term) ||
          inv.patient?.firstName.toLowerCase().includes(term) ||
          inv.patient?.lastName.toLowerCase().includes(term) ||
          inv.notes?.toLowerCase().includes(term)
        );
      }
    }
    
    setFilteredInvoices(filtered);
  }, [invoices, filters]);

  // Utility functions
  const findById = useCallback((id: string): IInvoice | undefined => {
    return invoices.find(invoice => invoice.id === id);
  }, [invoices]);

  const getInvoicesByPatient = useCallback((patientId: string): IInvoice[] => {
    return invoices.filter(inv => inv.patientId === patientId);
  }, [invoices]);

  const getStats = useCallback((): IInvoiceStats => {
    const now = new Date();
    const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const currentYear = new Date(now.getFullYear(), 0, 1);
    
    return {
      total: invoices.length,
      draft: invoices.filter(i => i.status === InvoiceStatus.DRAFT).length,
      sent: invoices.filter(i => i.status === InvoiceStatus.SENT).length,
      paid: invoices.filter(i => i.status === InvoiceStatus.PAID).length,
      overdue: invoices.filter(i => i.status === InvoiceStatus.OVERDUE || isInvoiceOverdue(i)).length,
      cancelled: invoices.filter(i => i.status === InvoiceStatus.CANCELLED).length,
      totalAmount: invoices.reduce((sum, i) => sum + i.totalAmount, 0),
      paidAmount: invoices.filter(i => i.status === InvoiceStatus.PAID).reduce((sum, i) => sum + i.totalAmount, 0),
      pendingAmount: invoices.filter(i => i.status === InvoiceStatus.SENT).reduce((sum, i) => sum + i.totalAmount, 0),
      overdueAmount: invoices.filter(i => i.status === InvoiceStatus.OVERDUE || isInvoiceOverdue(i)).reduce((sum, i) => sum + i.totalAmount, 0),
      monthlyTotal: invoices.filter(i => i.issueDate >= currentMonth).reduce((sum, i) => sum + i.totalAmount, 0),
      yearlyTotal: invoices.filter(i => i.issueDate >= currentYear).reduce((sum, i) => sum + i.totalAmount, 0),
    };
  }, [invoices]);

  const refresh = useCallback(() => {
    return loadInvoices();
  }, [loadInvoices]);

  return {
    // Dati
    invoices: filteredInvoices.length > 0 ? filteredInvoices : invoices,
    allInvoices: invoices,
    isLoading,
    error,
    
    // Azioni CRUD
    createInvoice: createInvoiceHandler,
    updateInvoice,
    deleteInvoice,
    
    // Utilità
    refresh,
    findById,
    getInvoicesByPatient,
    getStats,
    clearError,
    
    // Stato
    hasData: invoices.length > 0,
    isEmpty: invoices.length === 0,
    
    // Helper per UI
    getStatusLabel,
    getStatusColor,
    formatCurrency,
    formatInvoiceDate
  };
};
