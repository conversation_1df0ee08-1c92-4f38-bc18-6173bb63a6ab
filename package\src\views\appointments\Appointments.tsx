import { <PERSON>, Badge, Button, Select, TextInput, Spinner, Alert } from "flowbite-react";
import { Icon } from "@iconify/react";
import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAppointments } from "../../hooks/useAppointments";
import { usePatients } from "../../hooks/usePatients";
import {
  AppointmentStatus,
  IAppointmentFilters,
  formatAppointmentDate,
  formatAppointmentTime,
  getStatusLabel,
  getStatusColor
} from "../../types/appointments/IAppointment";

const Appointments = () => {
  const navigate = useNavigate();

  // Hook per appuntamenti e pazienti
  const {
    appointments,
    isLoading,
    error,
    clearError,
    getStats,
    deleteAppointment
  } = useAppointments();

  const { findById: findPatientById } = usePatients();

  // Stato locale per filtri e ricerca
  const [filter, setFilter] = useState("Oggi");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<AppointmentStatus | "">("");
  const [filteredAppointments, setFilteredAppointments] = useState(appointments);

  // Filtra gli appuntamenti in base alla selezione
  useEffect(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);

    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);

    let filtered = [...appointments];

    // Filtro per periodo
    switch (filter) {
      case "Oggi":
        filtered = filtered.filter(a => {
          const aptDate = new Date(a.startTime);
          aptDate.setHours(0, 0, 0, 0);
          return aptDate.getTime() === today.getTime();
        });
        break;
      case "Domani":
        filtered = filtered.filter(a => {
          const aptDate = new Date(a.startTime);
          aptDate.setHours(0, 0, 0, 0);
          return aptDate.getTime() === tomorrow.getTime();
        });
        break;
      case "Questa Settimana":
        filtered = filtered.filter(a => {
          const aptDate = new Date(a.startTime);
          return aptDate >= startOfWeek && aptDate < endOfWeek;
        });
        break;
      case "Questo Mese":
        filtered = filtered.filter(a => {
          const aptDate = new Date(a.startTime);
          return aptDate >= startOfMonth && aptDate < endOfMonth;
        });
        break;
      default:
        filtered = appointments;
    }

    // Filtro per stato
    if (statusFilter) {
      filtered = filtered.filter(a => a.status === statusFilter);
    }

    // Filtro per ricerca
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(a =>
        a.title.toLowerCase().includes(term) ||
        a.description?.toLowerCase().includes(term) ||
        a.patient?.firstName.toLowerCase().includes(term) ||
        a.patient?.lastName.toLowerCase().includes(term) ||
        a.notes?.toLowerCase().includes(term)
      );
    }

    // Ordina per data e ora
    filtered.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());

    setFilteredAppointments(filtered);
  }, [appointments, filter, statusFilter, searchTerm]);

  // Gestori eventi
  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilter(e.target.value);
  };

  const handleStatusFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value as AppointmentStatus | "");
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleNewAppointment = () => {
    navigate('/appointments/new');
  };

  const handleEditAppointment = (appointmentId: string) => {
    navigate(`/appointments/edit/${appointmentId}`);
  };

  const handleDeleteAppointment = async (appointmentId: string) => {
    if (window.confirm('Sei sicuro di voler eliminare questo appuntamento?')) {
      await deleteAppointment(appointmentId);
    }
  };

  // Loading state
  if (isLoading && appointments.length === 0) {
    return (
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <Spinner size="lg" />
            <p className="mt-2">Caricamento appuntamenti...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        {/* Gestione errori */}
        {error && (
          <Alert color="failure" onDismiss={clearError} className="mb-6">
            <span className="font-medium">Errore!</span> {error}
          </Alert>
        )}

        <div className="flex flex-col gap-4 mb-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <h5 className="card-title">
              Gestione Appuntamenti
              <span className="text-sm font-normal text-gray-500 ml-2">
                ({filteredAppointments.length} appuntamenti)
              </span>
            </h5>
            <div className="flex flex-wrap gap-3 items-center">
              <Button
                color="primary"
                className="flex items-center gap-2"
                onClick={handleNewAppointment}
                disabled={isLoading}
              >
                <Icon icon="solar:add-circle-outline" height={20} />
                <span className="hidden sm:inline">Nuovo Appuntamento</span>
                <span className="sm:hidden">Nuovo</span>
              </Button>
              <Button color="secondary" className="flex items-center gap-2" as={Link} to="/calendar">
                <Icon icon="solar:calendar-mark-line-duotone" height={20} />
                <span>Calendario</span>
              </Button>
            </div>
          </div>

          {/* Filtri */}
          <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
            <TextInput
              id="search"
              placeholder="Cerca paziente, trattamento..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-full sm:w-64"
              icon={Icon}
            />
            <Select
              id="date-filter"
              className="w-full sm:w-auto"
              value={filter}
              onChange={handleFilterChange}
            >
              <option value="Oggi">Oggi</option>
              <option value="Domani">Domani</option>
              <option value="Questa Settimana">Questa Settimana</option>
              <option value="Questo Mese">Questo Mese</option>
            </Select>
            <Select
              id="status-filter"
              className="w-full sm:w-auto"
              value={statusFilter}
              onChange={handleStatusFilterChange}
            >
              <option value="">Tutti gli stati</option>
              {Object.values(AppointmentStatus).map(status => (
                <option key={status} value={status}>
                  {getStatusLabel(status)}
                </option>
              ))}
            </Select>
          </div>
        </div>
        <div className="overflow-x-auto">
          <Table hoverable className="table-auto w-full">
            <Table.Head>
              <Table.HeadCell className="p-4">Paziente</Table.HeadCell>
              <Table.HeadCell className="p-4">Trattamento</Table.HeadCell>
              <Table.HeadCell className="p-4">Data</Table.HeadCell>
              <Table.HeadCell className="p-4">Ora</Table.HeadCell>
              <Table.HeadCell className="p-4">Stato</Table.HeadCell>
              <Table.HeadCell className="p-4">Azioni</Table.HeadCell>
            </Table.Head>
            <Table.Body className="divide-y divide-border dark:divide-darkborder">
              {filteredAppointments.length > 0 ? (
                filteredAppointments.map((appointment) => {
                  // Trova il paziente associato
                  const patient = appointment.patient || findPatientById(appointment.patientId);

                  return (
                    <Table.Row key={appointment.id}>
                      <Table.Cell className="p-4">
                        <div className="flex gap-2 items-center">
                          <div>
                            <h6 className="text-sm font-medium">
                              {patient ?
                                `${patient.firstName} ${patient.lastName}` :
                                'Paziente non trovato'
                              }
                            </h6>
                            {patient?.phone && (
                              <p className="text-xs text-gray-500">{patient.phone}</p>
                            )}
                          </div>
                        </div>
                      </Table.Cell>
                      <Table.Cell className="p-4">
                        <div>
                          <p className="text-sm font-medium">{appointment.title}</p>
                          {appointment.description && (
                            <p className="text-xs text-gray-500">{appointment.description}</p>
                          )}
                        </div>
                      </Table.Cell>
                      <Table.Cell className="p-4">
                        <p className="text-sm">{formatAppointmentDate(appointment.startTime)}</p>
                      </Table.Cell>
                      <Table.Cell className="p-4">
                        <p className="text-sm">
                          {formatAppointmentTime(appointment.startTime)} - {formatAppointmentTime(appointment.endTime)}
                        </p>
                      </Table.Cell>
                      <Table.Cell className="p-4">
                        <Badge color={getStatusColor(appointment.status)}>
                          {getStatusLabel(appointment.status)}
                        </Badge>
                      </Table.Cell>
                      <Table.Cell className="p-4">
                        <div className="flex gap-2">
                          <Button
                            color="primary"
                            size="xs"
                            onClick={() => handleEditAppointment(appointment.id)}
                            disabled={isLoading}
                            title="Modifica appuntamento"
                          >
                            <Icon icon="solar:pen-outline" height={16} />
                          </Button>
                          <Button
                            color="secondary"
                            size="xs"
                            onClick={() => navigate(`/calendar?date=${formatAppointmentDate(appointment.startTime)}`)}
                            title="Visualizza nel calendario"
                          >
                            <Icon icon="solar:calendar-mark-line-duotone" height={16} />
                          </Button>
                          <Button
                            color="failure"
                            size="xs"
                            onClick={() => handleDeleteAppointment(appointment.id)}
                            disabled={isLoading}
                            title="Elimina appuntamento"
                          >
                            <Icon icon="solar:trash-bin-minimalistic-outline" height={16} />
                          </Button>
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  );
                })
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={6} className="text-center py-8">
                    <div className="flex flex-col items-center gap-3">
                      <Icon icon="solar:calendar-minimalistic-outline" height={48} className="text-gray-400" />
                      <p className="text-gray-500">
                        {searchTerm || statusFilter ?
                          'Nessun appuntamento trovato con i filtri selezionati' :
                          'Nessun appuntamento trovato per il periodo selezionato'
                        }
                      </p>
                      <Button
                        color="primary"
                        size="sm"
                        onClick={handleNewAppointment}
                        className="flex items-center gap-2"
                      >
                        <Icon icon="solar:add-circle-outline" height={16} />
                        Crea primo appuntamento
                      </Button>
                    </div>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table>
        </div>
      </div>
    </>
  );
};

export default Appointments;
