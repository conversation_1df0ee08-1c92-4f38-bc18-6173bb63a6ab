/**
 * API Routes per la gestione dei prodotti
 *
 * Queste routes gestiscono tutte le operazioni CRUD sui prodotti
 * utilizzando Prisma per interagire con il database PostgreSQL.
 *
 * Compatibile con Vercel Serverless Functions
 */

import { ProductDatabaseService } from '../package/src/services/ProductDatabaseService';
import { IProduct } from '../package/src/types/inventory/IProduct';

/**
 * Handler principale per le API dei prodotti
 * GET /api/products - Ottiene tutti i prodotti
 * POST /api/products - Crea un nuovo prodotto
 */
export default async function handler(req: Request): Promise<Response> {
  try {
    if (req.method === 'POST') {
      // Creazione nuovo prodotto
      const productData: Omit<IProduct, 'id'> = await req.json();

      // Validazione base dei dati richiesti
      if (!productData.name || !productData.category || productData.quantity === undefined) {
        return new Response(JSON.stringify({
          success: false,
          error: 'Dati mancanti: name, category e quantity sono obbligatori'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Validazione quantità
      if (productData.quantity < 0) {
        return new Response(JSON.stringify({
          success: false,
          error: 'La quantità non può essere negativa'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      const product = await ProductDatabaseService.createProduct(productData);

      return new Response(JSON.stringify(product), {
        status: 201,
        headers: { 'Content-Type': 'application/json' }
      });

    } else if (req.method === 'GET') {
      // Recupero di tutti i prodotti
      const products = await ProductDatabaseService.getAllProducts();

      return new Response(JSON.stringify(products), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });

    } else {
      // Metodo non supportato
      return new Response(JSON.stringify({
        success: false,
        error: `Metodo ${req.method} non supportato`
      }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('❌ Errore API prodotti:', error);

    return new Response(JSON.stringify({
      success: false,
      error: 'Errore interno del server durante la gestione dei prodotti'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
