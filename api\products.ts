import { ProductDatabaseService } from '../package/src/services/ProductDatabaseService';

export default async function handler(req: Request): Promise<Response> {
  if (req.method === 'POST') {
    const body = await req.json();
    const product = await ProductDatabaseService.createProduct(body);
    return new Response(JSON.stringify(product), {
      status: 201,
      headers: { 'content-type': 'application/json' }
    });
  }

  const list = await ProductDatabaseService.getAllProducts();
  return new Response(JSON.stringify(list), {
    status: 200,
    headers: { 'content-type': 'application/json' }
  });
}
