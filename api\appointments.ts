import { AppointmentDatabaseService } from "../package/src/services/AppointmentDatabaseService";

export default async function handler(req: Request): Promise<Response> {
  try {
    if (req.method === "POST") {
      const data = await req.json();
      const appointment = await AppointmentDatabaseService.createAppointment(data);
      return new Response(JSON.stringify(appointment), {
        status: 201,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    const appointments = await AppointmentDatabaseService.getAllAppointments();
    return new Response(JSON.stringify(appointments), {
      status: 200,
      headers: { "Content-Type": "application/json" }
    });
  } catch (error) {
    console.error("❌ Errore API appointments:", error);
    return new Response(JSON.stringify({ 
      error: "Errore interno del server",
      details: error instanceof Error ? error.message : "Errore sconosciuto"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
}
