import { AppointmentDatabaseService } from '../package/src/services/AppointmentDatabaseService';
export default async function handler(req: Request): Promise<Response> {
  if (req.method === 'POST') {
    const data = await req.json();
    const appt = await AppointmentDatabaseService.createAppointment(data);
    return new Response(JSON.stringify(appt), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
  }
  const list = await AppointmentDatabaseService.getAllAppointments();
  return new Response(JSON.stringify(list), {
    status: 200,
    headers: { 'Content-Type': 'application/json' }
  });
}
