/**
 * API Routes per la gestione degli appuntamenti
 *
 * Queste routes gestiscono tutte le operazioni CRUD sugli appuntamenti
 * utilizzando Prisma per interagire con il database PostgreSQL.
 *
 * Compatibile con Vercel Serverless Functions
 */

import { AppointmentDatabaseService } from '../package/src/services/AppointmentDatabaseService';
import { ICreateAppointment } from '../package/src/types/appointments/IAppointment';

/**
 * Handler principale per le API degli appuntamenti
 * GET /api/appointments - Ottiene tutti gli appuntamenti
 * POST /api/appointments - Crea un nuovo appuntamento
 */
export default async function handler(req: Request): Promise<Response> {
  try {
    if (req.method === 'POST') {
      // Creazione nuovo appuntamento
      const appointmentData: ICreateAppointment = await req.json();

      // Validazione base dei dati richiesti
      if (!appointmentData.patientId || !appointmentData.title || !appointmentData.startTime || !appointmentData.endTime) {
        return new Response(JSON.stringify({
          success: false,
          error: 'Dati mancanti: patientId, title, startTime e endTime sono obbligatori'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Conversione delle date da string a Date se necessario
      const processedData: ICreateAppointment = {
        ...appointmentData,
        startTime: new Date(appointmentData.startTime),
        endTime: new Date(appointmentData.endTime)
      };

      const appointment = await AppointmentDatabaseService.createAppointment(processedData);

      return new Response(JSON.stringify(appointment), {
        status: 201,
        headers: { 'Content-Type': 'application/json' }
      });

    } else if (req.method === 'GET') {
      // Recupero di tutti gli appuntamenti
      const appointments = await AppointmentDatabaseService.getAllAppointments();

      return new Response(JSON.stringify(appointments), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });

    } else {
      // Metodo non supportato
      return new Response(JSON.stringify({
        success: false,
        error: `Metodo ${req.method} non supportato`
      }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('❌ Errore API appuntamenti:', error);

    return new Response(JSON.stringify({
      success: false,
      error: 'Errore interno del server durante la gestione degli appuntamenti'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
