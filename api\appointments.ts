import { AppointmentDatabaseService } from '../package/src/services/AppointmentDatabaseService';
export default async function handler(req, res) {
  if (req.method === 'POST') {
    const data = await req.json();
    const appt = await AppointmentDatabaseService.createAppointment(data);
    return Response.json(appt);
  }
  const list = await AppointmentDatabaseService.getAllAppointments();
  return Response.json(list);
}
