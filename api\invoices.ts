/**
 * API Routes per la gestione delle fatture
 *
 * Queste routes gestiscono tutte le operazioni CRUD sulle fatture
 * utilizzando Prisma per interagire con il database PostgreSQL.
 *
 * Compatibile con Vercel Serverless Functions
 */

import { InvoiceDatabaseService } from '../package/src/services/InvoiceDatabaseService';
import { ICreateInvoice } from '../package/src/types/invoices/IInvoice';

/**
 * Handler principale per le API delle fatture
 * GET /api/invoices - Ottiene tutte le fatture
 * POST /api/invoices - Crea una nuova fattura
 */
export default async function handler(req: Request): Promise<Response> {
  try {
    if (req.method === 'POST') {
      // Creazione nuova fattura
      const invoiceData: ICreateInvoice = await req.json();

      // Validazione base dei dati richiesti
      if (!invoiceData.patientId || !invoiceData.amount || !invoiceData.issueDate) {
        return new Response(JSON.stringify({
          success: false,
          error: 'Dati mancanti: patientId, amount e issueDate sono obbligatori'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Conversione delle date da string a Date se necessario
      const processedData: ICreateInvoice = {
        ...invoiceData,
        issueDate: new Date(invoiceData.issueDate),
        dueDate: invoiceData.dueDate ? new Date(invoiceData.dueDate) : undefined
      };

      const invoice = await InvoiceDatabaseService.createInvoice(processedData);

      return new Response(JSON.stringify(invoice), {
        status: 201,
        headers: { 'Content-Type': 'application/json' }
      });

    } else if (req.method === 'GET') {
      // Recupero di tutte le fatture
      const invoices = await InvoiceDatabaseService.getAllInvoices();

      return new Response(JSON.stringify(invoices), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });

    } else {
      // Metodo non supportato
      return new Response(JSON.stringify({
        success: false,
        error: `Metodo ${req.method} non supportato`
      }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('❌ Errore API fatture:', error);

    return new Response(JSON.stringify({
      success: false,
      error: 'Errore interno del server durante la gestione delle fatture'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
