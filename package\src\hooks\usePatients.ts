/**
 * Hook personalizzato per la gestione dei pazienti
 *
 * <PERSON>o hook fornisce un'interfaccia semplice per interagire con i pazienti
 * nel database, utilizzando il nuovo servizio API che gestisce automaticamente
 * l'ambiente (browser con mock data o server con database reale).
 *
 * Caratteristiche:
 * - Caricamento automatico dei pazienti
 * - Operazioni CRUD semplificate
 * - Gestione dello stato di caricamento e errori
 * - Compatibilità con componenti React esistenti
 * - Supporto sia per mock data che database reale
 */

import { useState, useEffect, useCallback } from 'react';
import { IPatient, ICreatePatient } from '../types/patients/IPatient';
import { PatientApiService } from '../services/PatientApiService';

interface UsePatientOptions {
  autoLoad?: boolean;
}

/**
 * Hook per la gestione dei pazienti
 * @param options Opzioni di configurazione
 * @returns Oggetto con dati e funzioni per gestire i pazienti
 */
export const usePatients = (options: UsePatientOptions = {}) => {
  const { autoLoad = true } = options;

  // Stato locale per i pazienti
  const [patients, setPatients] = useState<IPatient[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Carica tutti i pazienti dal database
   */
  const loadPatients = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      const patientsData = await PatientApiService.getAllPatients();
      setPatients(patientsData);
      console.log('✅ Pazienti caricati:', patientsData.length);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nel caricamento dei pazienti';
      setError(errorMessage);
      console.error('❌ Errore caricamento pazienti:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Carica i pazienti automaticamente se richiesto
  useEffect(() => {
    if (autoLoad && patients.length === 0 && !isLoading) {
      loadPatients();
    }
  }, [autoLoad, patients.length, isLoading, loadPatients]);

  /**
   * Crea un nuovo paziente
   * @param patientData Dati del paziente da creare
   * @returns Promise con il paziente creato o null in caso di errore
   */
  const handleCreatePatient = async (patientData: ICreatePatient): Promise<IPatient | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const newPatient = await PatientApiService.createPatient(patientData);

      // Aggiorna la lista locale
      setPatients(prev => [...prev, newPatient].sort((a, b) =>
        a.lastName.localeCompare(b.lastName) || a.firstName.localeCompare(b.firstName)
      ));

      console.log('✅ Paziente creato con successo:', newPatient.id);
      return newPatient;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nella creazione del paziente';
      setError(errorMessage);
      console.error('❌ Errore creazione paziente:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Aggiorna un paziente esistente
   * @param id ID del paziente da aggiornare
   * @param patientData Dati da aggiornare
   * @returns Promise con il paziente aggiornato o null in caso di errore
   */
  const handleUpdatePatient = async (
    id: string,
    patientData: Partial<ICreatePatient>
  ): Promise<IPatient | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const updatedPatient = await PatientApiService.updatePatient(id, patientData);

      // Aggiorna la lista locale
      setPatients(prev => prev.map(patient =>
        patient.id === id ? updatedPatient : patient
      ).sort((a, b) =>
        a.lastName.localeCompare(b.lastName) || a.firstName.localeCompare(b.firstName)
      ));

      console.log('✅ Paziente aggiornato con successo:', updatedPatient.id);
      return updatedPatient;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nell\'aggiornamento del paziente';
      setError(errorMessage);
      console.error('❌ Errore aggiornamento paziente:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Elimina un paziente
   * @param id ID del paziente da eliminare
   * @returns Promise con true se eliminato con successo, false altrimenti
   */
  const handleDeletePatient = async (id: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await PatientApiService.deletePatient(id);

      if (result) {
        // Rimuovi dalla lista locale
        setPatients(prev => prev.filter(patient => patient.id !== id));
        console.log('✅ Paziente eliminato con successo:', id);
      }

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nell\'eliminazione del paziente';
      setError(errorMessage);
      console.error('❌ Errore eliminazione paziente:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Cerca pazienti per query
   * @param query Stringa di ricerca
   * @returns Promise con array di pazienti trovati
   */
  const handleSearchPatients = async (query: string): Promise<IPatient[]> => {
    setIsLoading(true);
    setError(null);

    try {
      const results = await PatientApiService.searchPatients(query);
      console.log(`✅ Trovati ${results.length} pazienti per query: "${query}"`);
      return results;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nella ricerca dei pazienti';
      setError(errorMessage);
      console.error('❌ Errore ricerca pazienti:', err);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Ricarica la lista dei pazienti
   */
  const handleRefresh = async (): Promise<void> => {
    try {
      await loadPatients();
      console.log('✅ Lista pazienti ricaricata con successo');
    } catch (error) {
      console.error('❌ Errore ricaricamento pazienti:', error);
    }
  };

  /**
   * Pulisce l'errore corrente
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Trova un paziente per ID
   * @param id ID del paziente da trovare
   * @returns Paziente trovato o undefined
   */
  const findPatientById = useCallback((id: string): IPatient | undefined => {
    return patients.find((patient: IPatient) => patient.id === id);
  }, [patients]);

  /**
   * Filtra i pazienti localmente
   * @param filterFn Funzione di filtro
   * @returns Array di pazienti filtrati
   */
  const filterPatients = useCallback((filterFn: (patient: IPatient) => boolean): IPatient[] => {
    return patients.filter(filterFn);
  }, [patients]);

  /**
   * Ottiene statistiche sui pazienti
   * @returns Oggetto con statistiche
   */
  const getPatientStats = useCallback(() => {
    const total = patients.length;
    const withEmail = patients.filter((p: IPatient) => p.email).length;
    const withPhone = patients.filter((p: IPatient) => p.phone).length;
    const withFiscalCode = patients.filter((p: IPatient) => p.fiscalCode).length;

    return {
      total,
      withEmail,
      withPhone,
      withFiscalCode,
      completionRate: total > 0 ? Math.round((withEmail + withPhone + withFiscalCode) / (total * 3) * 100) : 0,
    };
  }, [patients]);

  // Ritorna l'oggetto con tutti i dati e le funzioni
  return {
    // Dati
    patients,
    isLoading,
    error,
    
    // Operazioni CRUD
    createPatient: handleCreatePatient,
    updatePatient: handleUpdatePatient,
    deletePatient: handleDeletePatient,
    searchPatients: handleSearchPatients,
    
    // Utilità
    refresh: handleRefresh,
    findById: findPatientById,
    filter: filterPatients,
    getStats: getPatientStats,
    clearError,
    
    // Stato
    hasPatients: patients.length > 0,
    isEmpty: patients.length === 0 && !isLoading,
  };
};

export default usePatients;
