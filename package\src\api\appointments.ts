import { AppointmentDatabaseService } from '../services/AppointmentDatabaseService';
import { Request, Response } from 'express';

export async function getAllAppointments(req: Request, res: Response) {
  try {
    const appointments = await AppointmentDatabaseService.getAllAppointments();
    res.json({ success: true, data: appointments, count: appointments.length });
  } catch (error) {
    console.error('❌ Errore API getAllAppointments:', error);
    res.status(500).json({
      success: false,
      error: 'Errore durante il caricamento degli appuntamenti',
      details: error instanceof Error ? error.message : 'Errore sconosciuto'
    });
  }
}

export async function getAppointmentById(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const appointment = await AppointmentDatabaseService.getAppointmentById(id);
    if (!appointment) {
      return res.status(404).json({ success: false, error: 'Appuntamento non trovato' });
    }
    res.json({ success: true, data: appointment });
  } catch (error) {
    console.error('❌ Errore API getAppointmentById:', error);
    res.status(500).json({
      success: false,
      error: 'Errore durante il caricamento dell\'appuntamento',
      details: error instanceof Error ? error.message : 'Errore sconosciuto'
    });
  }
}

export async function createAppointment(req: Request, res: Response) {
  try {
    const appointmentData = req.body;
    if (!appointmentData.patientId || !appointmentData.title || !appointmentData.startTime || !appointmentData.endTime) {
      return res.status(400).json({ success: false, error: 'Dati obbligatori mancanti' });
    }
    const appointment = await AppointmentDatabaseService.createAppointment(appointmentData);
    res.status(201).json({ success: true, data: appointment, message: 'Appuntamento creato con successo' });
  } catch (error) {
    console.error('❌ Errore API createAppointment:', error);
    res.status(500).json({
      success: false,
      error: 'Errore durante la creazione dell\'appuntamento',
      details: error instanceof Error ? error.message : 'Errore sconosciuto'
    });
  }
}

export async function updateAppointment(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const appointmentData = req.body;
    const appointment = await AppointmentDatabaseService.updateAppointment(id, appointmentData);
    res.json({ success: true, data: appointment, message: 'Appuntamento aggiornato con successo' });
  } catch (error) {
    console.error('❌ Errore API updateAppointment:', error);
    res.status(500).json({
      success: false,
      error: 'Errore durante l\'aggiornamento dell\'appuntamento',
      details: error instanceof Error ? error.message : 'Errore sconosciuto'
    });
  }
}

export async function deleteAppointment(req: Request, res: Response) {
  try {
    const { id } = req.params;
    await AppointmentDatabaseService.deleteAppointment(id);
    res.json({ success: true, message: 'Appuntamento eliminato con successo' });
  } catch (error) {
    console.error('❌ Errore API deleteAppointment:', error);
    res.status(500).json({
      success: false,
      error: 'Errore durante l\'eliminazione dell\'appuntamento',
      details: error instanceof Error ? error.message : 'Errore sconosciuto'
    });
  }
}
