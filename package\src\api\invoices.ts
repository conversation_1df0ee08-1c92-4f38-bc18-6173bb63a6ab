import { NextFunction, Request, Response } from 'express';
import { InvoiceDatabaseService } from '../services/InvoiceDatabaseService';

// GET /api/invoices
export async function getAllInvoices(req: Request, res: Response, next: NextFunction) {
  try {
    const invoices = await InvoiceDatabaseService.getAllInvoices();
    res.json(invoices);
  } catch (err) {
    next(err);
  }
}

// GET /api/invoices/:id
export async function getInvoiceById(req: Request, res: Response, next: NextFunction) {
  try {
    const invoice = await InvoiceDatabaseService.getInvoiceById(req.params.id);
    if (!invoice) return res.status(404).json({ error: 'Invoice not found' });
    res.json(invoice);
  } catch (err) {
    next(err);
  }
}

// POST /api/invoices
export async function createInvoice(req: Request, res: Response, next: NextFunction) {
  try {
    const invoice = await InvoiceDatabaseService.createInvoice(req.body);
    res.status(201).json(invoice);
  } catch (err) {
    next(err);
  }
}

// PUT /api/invoices/:id
export async function updateInvoice(req: Request, res: Response, next: NextFunction) {
  try {
    const invoice = await InvoiceDatabaseService.updateInvoice(req.params.id, req.body);
    res.json(invoice);
  } catch (err) {
    next(err);
  }
}

// DELETE /api/invoices/:id
export async function deleteInvoice(req: Request, res: Response, next: NextFunction) {
  try {
    await InvoiceDatabaseService.deleteInvoice(req.params.id);
    res.status(204).end();
  } catch (err) {
    next(err);
  }
}
