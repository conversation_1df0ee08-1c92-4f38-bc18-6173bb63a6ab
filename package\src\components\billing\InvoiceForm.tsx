import { useState, useEffect } from 'react';
import { Button, Label, TextInput, Select, Textarea, Alert, Spinner } from 'flowbite-react';
import { useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { useInvoices } from '../../hooks/useInvoices';
import { usePatients } from '../../hooks/usePatients';
import {
  IInvoice,
  ICreateInvoice,
  InvoiceStatus,
  getStatusLabel,
  TAX_RATES,
  calculateTotalWithTax,
  generateInvoiceNumber
} from '../../types/invoices/IInvoice';

interface InvoiceFormProps {
  isEdit?: boolean;
  invoiceData?: IInvoice;
}

const InvoiceForm = ({ isEdit = false, invoiceData }: InvoiceFormProps) => {
  const navigate = useNavigate();

  // Hook per la gestione delle fatture e pazienti
  const { createInvoice, updateInvoice, isLoading, error, clearError } = useInvoices(false);
  const { patients } = usePatients();

  const [formData, setFormData] = useState({
    patientId: invoiceData?.patientId || '',
    invoiceNumber: invoiceData?.invoiceNumber || generateInvoiceNumber(),
    issueDate: invoiceData?.issueDate ?
      invoiceData.issueDate.toISOString().split('T')[0] :
      new Date().toISOString().split('T')[0],
    dueDate: invoiceData?.dueDate ?
      invoiceData.dueDate.toISOString().split('T')[0] : '',
    amount: invoiceData?.amount?.toString() || '',
    taxRate: '22', // Default 22%
    taxAmount: invoiceData?.taxAmount?.toString() || '',
    totalAmount: invoiceData?.totalAmount?.toString() || '',
    status: invoiceData?.status || InvoiceStatus.DRAFT,
    description: invoiceData?.description || '',
    notes: invoiceData?.notes || ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Auto-calcola i totali quando cambia l'importo o l'aliquota IVA
    if (name === 'amount' || name === 'taxRate') {
      const amount = name === 'amount' ? parseFloat(value) || 0 : parseFloat(formData.amount) || 0;
      const taxRate = name === 'taxRate' ? parseFloat(value) || 0 : parseFloat(formData.taxRate) || 0;

      if (amount > 0) {
        const { taxAmount, totalAmount } = calculateTotalWithTax(amount, taxRate);
        setFormData(prev => ({
          ...prev,
          taxAmount: taxAmount.toString(),
          totalAmount: totalAmount.toString()
        }));
      }
    }
  };

  // Calcola automaticamente i totali quando il form viene caricato
  useEffect(() => {
    if (formData.amount && !formData.totalAmount) {
      const amount = parseFloat(formData.amount) || 0;
      const taxRate = parseFloat(formData.taxRate) || 0;

      if (amount > 0) {
        const { taxAmount, totalAmount } = calculateTotalWithTax(amount, taxRate);
        setFormData(prev => ({
          ...prev,
          taxAmount: taxAmount.toString(),
          totalAmount: totalAmount.toString()
        }));
      }
    }
  }, [formData.amount, formData.taxRate, formData.totalAmount]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError(); // Pulisce eventuali errori precedenti

    try {
      // Validazione base
      if (!formData.patientId || !formData.amount || !formData.issueDate) {
        alert('Paziente, importo e data di emissione sono obbligatori');
        return;
      }

      const amount = parseFloat(formData.amount);
      if (isNaN(amount) || amount <= 0) {
        alert('L\'importo deve essere un numero positivo');
        return;
      }

      // Prepara i dati per il salvataggio
      const invoiceDataToSave: ICreateInvoice = {
        patientId: formData.patientId,
        invoiceNumber: formData.invoiceNumber.trim(),
        issueDate: new Date(formData.issueDate),
        dueDate: formData.dueDate ? new Date(formData.dueDate) : undefined,
        amount: amount,
        taxAmount: parseFloat(formData.taxAmount) || undefined,
        description: formData.description.trim() || undefined,
        notes: formData.notes.trim() || undefined,
        status: formData.status,
      };

      let result;
      if (isEdit && invoiceData?.id) {
        // Modalità modifica
        result = await updateInvoice(invoiceData.id, invoiceDataToSave);
      } else {
        // Modalità creazione
        result = await createInvoice(invoiceDataToSave);
      }

      if (result) {
        console.log(`✅ Fattura ${isEdit ? 'aggiornata' : 'creata'} con successo:`, result);
        // Reindirizza alla lista fatture dopo il salvataggio
        navigate('/billing');
      } else {
        console.error(`❌ Errore durante ${isEdit ? 'l\'aggiornamento' : 'la creazione'} della fattura`);
      }
    } catch (error) {
      console.error('Errore nel form:', error);
    }
  };

  return (
    <form className="space-y-6" onSubmit={handleSubmit}>
      {/* Gestione errori */}
      {error && (
        <Alert color="failure" onDismiss={clearError}>
          <span className="font-medium">Errore!</span> {error}
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="patientId" value="Paziente *" />
          </div>
          <Select
            id="patientId"
            name="patientId"
            value={formData.patientId}
            onChange={handleChange}
            required
            disabled={isLoading}
          >
            <option value="">Seleziona un paziente</option>
            {patients.map(patient => (
              <option key={patient.id} value={patient.id}>
                {patient.firstName} {patient.lastName}
              </option>
            ))}
          </Select>
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="invoiceNumber" value="Numero Fattura *" />
          </div>
          <TextInput
            id="invoiceNumber"
            name="invoiceNumber"
            value={formData.invoiceNumber}
            onChange={handleChange}
            placeholder="FAT-2024-001234"
            required
            disabled={isLoading}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="issueDate" value="Data Emissione *" />
          </div>
          <TextInput
            id="issueDate"
            name="issueDate"
            type="date"
            value={formData.issueDate}
            onChange={handleChange}
            required
            disabled={isLoading}
          />
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="dueDate" value="Data Scadenza" />
          </div>
          <TextInput
            id="dueDate"
            name="dueDate"
            type="date"
            value={formData.dueDate}
            onChange={handleChange}
            disabled={isLoading}
          />
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="status" value="Stato" />
          </div>
          <Select
            id="status"
            name="status"
            value={formData.status}
            onChange={handleChange}
            disabled={isLoading}
          >
            {Object.values(InvoiceStatus).map(status => (
              <option key={status} value={status}>
                {getStatusLabel(status)}
              </option>
            ))}
          </Select>
        </div>
      </div>

      <div>
        <div className="mb-2 block">
          <Label htmlFor="description" value="Descrizione" />
        </div>
        <TextInput
          id="description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          placeholder="Descrizione del trattamento o servizio..."
          disabled={isLoading}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="amount" value="Importo Base *" />
          </div>
          <TextInput
            id="amount"
            name="amount"
            type="number"
            step="0.01"
            min="0"
            value={formData.amount}
            onChange={handleChange}
            placeholder="0.00"
            required
            disabled={isLoading}
          />
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="taxRate" value="Aliquota IVA %" />
          </div>
          <Select
            id="taxRate"
            name="taxRate"
            value={formData.taxRate}
            onChange={handleChange}
            disabled={isLoading}
          >
            {TAX_RATES.map(rate => (
              <option key={rate.value} value={rate.value}>
                {rate.label}
              </option>
            ))}
          </Select>
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="taxAmount" value="Importo IVA" />
          </div>
          <TextInput
            id="taxAmount"
            name="taxAmount"
            type="number"
            step="0.01"
            value={formData.taxAmount}
            placeholder="0.00"
            disabled
            className="bg-gray-50"
          />
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="totalAmount" value="Totale" />
          </div>
          <TextInput
            id="totalAmount"
            name="totalAmount"
            type="number"
            step="0.01"
            value={formData.totalAmount}
            placeholder="0.00"
            disabled
            className="bg-gray-50 font-medium"
          />
        </div>
      </div>

      <div>
        <div className="mb-2 block">
          <Label htmlFor="notes" value="Note" />
        </div>
        <Textarea
          id="notes"
          name="notes"
          value={formData.notes}
          onChange={handleChange}
          placeholder="Note aggiuntive per la fattura..."
          rows={3}
          disabled={isLoading}
        />
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          color="light"
          onClick={() => navigate('/billing')}
          disabled={isLoading}
        >
          Annulla
        </Button>
        <Button
          type="submit"
          color="primary"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Spinner size="sm" className="mr-2" />
              {isEdit ? 'Aggiornamento...' : 'Salvataggio...'}
            </>
          ) : (
            isEdit ? 'Aggiorna Fattura' : 'Salva Fattura'
          )}
        </Button>
      </div>
    </form>
  );
};

export default InvoiceForm;
