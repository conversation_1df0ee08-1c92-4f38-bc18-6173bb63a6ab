/**
 * Interfaccia per i pazienti - Allineata con schema Prisma
 *
 * Questa interfaccia definisce la struttura dei dati dei pazienti
 * nel gestionale dentisti, compatibile con il database PostgreSQL.
 */
export interface IPatient {
  id: string; // Cam<PERSON>to da number a string per compatibilità con Prisma CUID
  firstName: string; // Rinominato da name
  lastName: string; // Rinominato da surname e reso obbligatorio
  email?: string | null; // Compatibile con Prisma
  phone?: string | null; // Compatibile con Prisma
  dateOfBirth?: Date | string | null; // Rinominato da birthDate
  address?: string | null; // Compatibile con Prisma
  city?: string | null; // Compatibile con Prisma
  postalCode?: string | null; // Compatibile con Prisma
  fiscalCode?: string | null; // Compatibile con Prisma
  notes?: string | null; // Compatibile con Prisma
  createdAt?: Date | string;
  updatedAt?: Date | string;

  // Campi medici aggiuntivi (per compatibilità con versione esistente)
  pathologies?: string[];
  smoker?: boolean;
  drugs?: string[];
  anamnesis?: string;

  // Relazioni opzionali (quando incluse nelle query)
  appointments?: any[];
  invoices?: any[];
  files?: any[];
  patientUDIs?: any[];
}

/**
 * Interfaccia per i dati di creazione paziente nel form
 */
export interface ICreatePatient {
  firstName: string;
  lastName: string;
  email?: string | null;
  phone?: string | null;
  dateOfBirth?: Date | string | null;
  address?: string | null;
  city?: string | null;
  postalCode?: string | null;
  fiscalCode?: string | null;
  notes?: string | null;

  // Campi medici aggiuntivi
  pathologies?: string[];
  smoker?: boolean;
  drugs?: string[];
  anamnesis?: string;
}

/**
 * Interfaccia per i filtri di ricerca pazienti
 */
export interface IPatientFilters {
  search?: string;
  city?: string;
  dateOfBirthFrom?: Date;
  dateOfBirthTo?: Date;
}
