/**
 * Componente per testare la connessione al database
 * 
 * Questo componente fornisce un'interfaccia per testare
 * le operazioni database prima dell'integrazione completa.
 */

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'flowbite-react';
import { Icon } from '@iconify/react';
import { DatabaseTestService } from '../../services/DatabaseTestService';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message?: string;
  duration?: number;
}

const DatabaseTestComponent = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([
    { name: 'Connessione Database', status: 'pending' },
    { name: 'Operazioni CRUD', status: 'pending' },
    { name: 'Popolamento Dati', status: 'pending' }
  ]);

  const updateTestResult = (index: number, updates: Partial<TestResult>) => {
    setTestResults(prev => prev.map((result, i) => 
      i === index ? { ...result, ...updates } : result
    ));
  };

  const runTests = async () => {
    setIsRunning(true);
    
    try {
      // Test 1: Connessione
      updateTestResult(0, { status: 'running' });
      const startTime1 = Date.now();
      const connectionResult = await DatabaseTestService.testConnection();
      const duration1 = Date.now() - startTime1;
      
      updateTestResult(0, {
        status: connectionResult ? 'success' : 'error',
        message: connectionResult ? 'Database connesso correttamente' : 'Errore connessione database',
        duration: duration1
      });

      if (!connectionResult) {
        setIsRunning(false);
        return;
      }

      // Test 2: CRUD
      updateTestResult(1, { status: 'running' });
      const startTime2 = Date.now();
      const crudResult = await DatabaseTestService.testPatientOperations();
      const duration2 = Date.now() - startTime2;
      
      updateTestResult(1, {
        status: crudResult ? 'success' : 'error',
        message: crudResult ? 'Operazioni CRUD funzionanti' : 'Errore operazioni CRUD',
        duration: duration2
      });

      if (!crudResult) {
        setIsRunning(false);
        return;
      }

      // Test 3: Seed
      updateTestResult(2, { status: 'running' });
      const startTime3 = Date.now();
      const seedResult = await DatabaseTestService.seedDatabase();
      const duration3 = Date.now() - startTime3;
      
      updateTestResult(2, {
        status: seedResult ? 'success' : 'error',
        message: seedResult ? 'Dati di esempio caricati' : 'Errore caricamento dati',
        duration: duration3
      });

    } catch (error) {
      console.error('Errore durante i test:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const resetTests = () => {
    setTestResults([
      { name: 'Connessione Database', status: 'pending' },
      { name: 'Operazioni CRUD', status: 'pending' },
      { name: 'Popolamento Dati', status: 'pending' }
    ]);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Icon icon="solar:clock-circle-outline" className="text-gray-400" height={20} />;
      case 'running':
        return <Spinner size="sm" />;
      case 'success':
        return <Icon icon="solar:check-circle-bold" className="text-green-500" height={20} />;
      case 'error':
        return <Icon icon="solar:close-circle-bold" className="text-red-500" height={20} />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return 'gray';
      case 'running':
        return 'info';
      case 'success':
        return 'success';
      case 'error':
        return 'failure';
    }
  };

  const allTestsCompleted = testResults.every(test => test.status === 'success' || test.status === 'error');
  const allTestsPassed = testResults.every(test => test.status === 'success');

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Test Database
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Verifica la connessione e le operazioni del database Neon
          </p>
        </div>

        <div className="space-y-3">
          {testResults.map((test, index) => (
            <Alert key={index} color={getStatusColor(test.status)} className="flex items-center">
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(test.status)}
                  <span className="font-medium">{test.name}</span>
                </div>
                <div className="text-right">
                  {test.message && (
                    <div className="text-sm">{test.message}</div>
                  )}
                  {test.duration && (
                    <div className="text-xs opacity-75">{test.duration}ms</div>
                  )}
                </div>
              </div>
            </Alert>
          ))}
        </div>

        {allTestsCompleted && allTestsPassed && (
          <Alert color="success">
            <div className="flex items-center space-x-2">
              <Icon icon="solar:check-circle-bold" height={20} />
              <span className="font-medium">
                Tutti i test completati con successo! Il database è pronto per l'integrazione.
              </span>
            </div>
          </Alert>
        )}

        <div className="flex justify-center space-x-3">
          <Button
            color="primary"
            onClick={runTests}
            disabled={isRunning}
            className="flex items-center space-x-2"
          >
            {isRunning ? (
              <>
                <Spinner size="sm" />
                <span>Test in corso...</span>
              </>
            ) : (
              <>
                <Icon icon="solar:play-circle-outline" height={20} />
                <span>Avvia Test</span>
              </>
            )}
          </Button>

          <Button
            color="light"
            onClick={resetTests}
            disabled={isRunning}
            className="flex items-center space-x-2"
          >
            <Icon icon="solar:refresh-outline" height={20} />
            <span>Reset</span>
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default DatabaseTestComponent;
