/**
 * Servizio API per la gestione dei prodotti
 *
 * Questo servizio fornisce un'interfaccia unificata per le operazioni
 * sui prodotti, utilizzando sempre le API reali per tutte le operazioni.
 */

import { IProduct } from '../types/inventory/IProduct';

export class ProductApiService {
  /**
   * Ottiene tutti i prodotti
   * Utilizza sempre l'API reale per il recupero dei prodotti
   */
  static async getAllProducts(): Promise<IProduct[]> {
    try {
      const res = await fetch('/api/products');

      if (!res.ok) {
        throw new Error(`Errore HTTP: ${res.status}`);
      }

      const products = await res.json();
      console.log('📊 Prodotti caricati:', products.length);
      return products;
    } catch (error) {
      console.error('❌ Errore caricamento prodotti:', error);
      throw error;
    }
  }

  /**
   * Crea un nuovo prodotto
   * Utilizza sempre l'API reale per la creazione di prodotti
   */
  static async createProduct(data: Omit<IProduct, 'id'>): Promise<IProduct> {
    try {
      const res = await fetch('/api/products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      if (!res.ok) {
        throw new Error(`Errore HTTP: ${res.status}`);
      }

      const result = await res.json();
      console.log('✅ Prodotto creato:', result.id);
      return result;
    } catch (error) {
      console.error('❌ Errore creazione prodotto:', error);
      throw error;
    }
  }
}
