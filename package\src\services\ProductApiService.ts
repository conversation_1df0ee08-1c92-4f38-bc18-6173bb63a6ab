/**
 * Servizio API per la gestione dei prodotti
 *
 * Questo servizio fornisce un'interfaccia semplificata per le operazioni
 * sui prodotti utilizzando le API REST.
 */

import { IProduct } from '../types/inventory/IProduct';

// Mock data solo per testing
let mockProducts: IProduct[] = [];
if (process.env.NODE_ENV === 'test') {
  // Mock data per l'ambiente di test
  mockProducts = [
    {
      id: 1,
      name: 'Prodotto Test',
      category: 'Test Category',
      description: 'Prodotto di test',
      quantity: 10,
      unit: 'pz',
      minQuantity: 5,
      price: 29.99,
      supplier: 'Test Supplier',
      location: 'Magazzino A',
      notes: 'Note di test'
    }
  ];
}

// Type aliases per compatibilità con la richiesta
type ProductInput = Omit<IProduct, 'id'>;
type Product = IProduct;

// create a new product
export const createProduct = async (data: ProductInput) => {
  const res = await fetch('/api/products', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error(`HTTP ${res.status}`);
  return res.json() as Promise<Product>;
};

// get all products
export const getAllProducts = async () => {
  const res = await fetch('/api/products');
  if (!res.ok) throw new Error(`HTTP ${res.status}`);
  return res.json() as Promise<Product[]>;
};
