import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { Spin<PERSON>, <PERSON>ert } from 'flowbite-react';
import AppointmentForm from '../../components/appointments/AppointmentForm';
import { useAppointments } from '../../hooks/useAppointments';
import { IAppointment } from '../../types/appointments/IAppointment';

const EditAppointment = () => {
  const { id } = useParams<{ id: string }>();
  const [appointment, setAppointment] = useState<IAppointment | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Hook per la gestione degli appuntamenti
  const { appointments, findById } = useAppointments();

  // Carica i dati dell'appuntamento
  useEffect(() => {
    if (!id) {
      setError('ID appuntamento non valido');
      setLoading(false);
      return;
    }

    // Cerca l'appuntamento nella lista caricata
    const foundAppointment = findById(id);
    
    if (foundAppointment) {
      setAppointment(foundAppointment);
      setLoading(false);
    } else if (appointments.length > 0) {
      // Se la lista è caricata ma l'appuntamento non è trovato
      setError('Appuntamento non trovato');
      setLoading(false);
    }
    // Se appointments.length === 0, aspetta che vengano caricati
  }, [id, appointments, findById]);

  if (loading) {
    return (
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <Spinner size="lg" />
            <p className="mt-2">Caricamento appuntamento...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <Alert color="failure">
          <span className="font-medium">Errore!</span> {error}
        </Alert>
        <div className="mt-4">
          <Link 
            to="/appointments" 
            className="inline-flex items-center gap-2 text-primary hover:underline"
          >
            <Icon icon="solar:arrow-left-linear" height={16} />
            Torna alla lista appuntamenti
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-2">
            <h5 className="card-title">
              Modifica Appuntamento
              {appointment && (
                <span className="text-sm font-normal text-gray-500 ml-2">
                  {appointment.title}
                </span>
              )}
            </h5>
            <Link to="/appointments" className="text-gray-500 hover:text-primary">
              <Icon icon="solar:arrow-left-linear" height={20} />
              <span className="sr-only">Torna alla lista appuntamenti</span>
            </Link>
          </div>
        </div>

        {appointment && <AppointmentForm isEdit={true} appointmentData={appointment} />}
      </div>
    </>
  );
};

export default EditAppointment;
