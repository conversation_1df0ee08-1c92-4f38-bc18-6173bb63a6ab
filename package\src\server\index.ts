/**
 * Entry point per il server API
 */

import app from './app';

const PORT = process.env.API_PORT || 3001;

app.listen(PORT, () => {
  console.log(`🚀 Server API in esecuzione su http://localhost:${PORT}`);
  console.log(`📊 Health check disponibile su http://localhost:${PORT}/health`);
  console.log(`👥 API Pazienti disponibile su http://localhost:${PORT}/api/patients`);
  console.log(`🌍 CORS configurato per: ${process.env.FRONTEND_URL || 'http://localhost:5174'}`);
});
