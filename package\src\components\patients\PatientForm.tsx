import { useState } from 'react';
import { Button, Label, TextInput, Select, Textarea, Alert, Spinner } from 'flowbite-react';
import { useNavigate } from 'react-router-dom';
import { usePatients } from '../../hooks/usePatients';
import { IPatient, ICreatePatient } from '../../types/patients/IPatient';

interface PatientFormProps {
  isEdit?: boolean;
  patientData?: IPatient;
}

const PatientForm = ({ isEdit = false, patientData }: PatientFormProps) => {
  const navigate = useNavigate();

  // Hook per la gestione dei pazienti
  const { createPatient, updatePatient, isLoading, error, clearError } = usePatients(false);

  const [formData, setFormData] = useState({
    firstName: patientData?.firstName || '',
    lastName: patientData?.lastName || '',
    phone: patientData?.phone || '',
    email: patientData?.email || '',
    dateOfBirth: patientData?.dateOfBirth ?
      (typeof patientData.dateOfBirth === 'string' ?
        patientData.dateOfBirth.split('T')[0] :
        patientData.dateOfBirth.toISOString().split('T')[0]
      ) : '',
    address: patientData?.address || '',
    city: patientData?.city || '',
    postalCode: patientData?.postalCode || '',
    fiscalCode: patientData?.fiscalCode || '',
    notes: patientData?.notes || ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError(); // Pulisce eventuali errori precedenti

    try {
      // Prepara i dati per il salvataggio
      const patientDataToSave: ICreatePatient = {
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim() || null,
        phone: formData.phone.trim() || null,
        dateOfBirth: formData.dateOfBirth || null,
        address: formData.address.trim() || null,
        city: formData.city.trim() || null,
        postalCode: formData.postalCode.trim() || null,
        fiscalCode: formData.fiscalCode.trim() || null,
        notes: formData.notes.trim() || null,
      };

      // Validazione base
      if (!patientDataToSave.firstName || !patientDataToSave.lastName) {
        alert('Nome e cognome sono obbligatori');
        return;
      }

      let result;
      if (isEdit && patientData?.id) {
        // Modalità modifica
        result = await updatePatient(patientData.id, patientDataToSave);
      } else {
        // Modalità creazione
        result = await createPatient(patientDataToSave);
      }

      if (result) {
        console.log(`✅ Paziente ${isEdit ? 'aggiornato' : 'creato'} con successo:`, result);
        // Reindirizza alla lista pazienti dopo il salvataggio
        navigate('/patients');
      } else {
        console.error(`❌ Errore durante ${isEdit ? 'l\'aggiornamento' : 'la creazione'} del paziente`);
      }
    } catch (error) {
      console.error('Errore nel form:', error);
    }
  };

  return (
    <form className="space-y-4" onSubmit={handleSubmit}>
      {/* Gestione errori */}
      {error && (
        <Alert color="failure" onDismiss={clearError}>
          <span className="font-medium">Errore!</span> {error}
        </Alert>
      )}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="firstName" value="Nome *" />
          </div>
          <TextInput
            id="firstName"
            name="firstName"
            value={formData.firstName}
            onChange={handleChange}
            placeholder="Mario"
            required
            disabled={isLoading}
          />
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="lastName" value="Cognome *" />
          </div>
          <TextInput
            id="lastName"
            name="lastName"
            value={formData.lastName}
            onChange={handleChange}
            placeholder="Rossi"
            required
            disabled={isLoading}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

        <div>
          <div className="mb-2 block">
            <Label htmlFor="phone" value="Telefono" />
          </div>
          <TextInput
            id="phone"
            name="phone"
            type="tel"
            value={formData.phone}
            onChange={handleChange}
            placeholder="+39 333 1234567"
            disabled={isLoading}
          />
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="email" value="Email" />
          </div>
          <TextInput
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleChange}
            placeholder="<EMAIL>"
            disabled={isLoading}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

        <div>
          <div className="mb-2 block">
            <Label htmlFor="dateOfBirth" value="Data di nascita" />
          </div>
          <TextInput
            id="dateOfBirth"
            name="dateOfBirth"
            type="date"
            value={formData.dateOfBirth}
            onChange={handleChange}
            disabled={isLoading}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="city" value="Città" />
          </div>
          <TextInput
            id="city"
            name="city"
            value={formData.city}
            onChange={handleChange}
            placeholder="Milano"
            disabled={isLoading}
          />
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="postalCode" value="CAP" />
          </div>
          <TextInput
            id="postalCode"
            name="postalCode"
            value={formData.postalCode}
            onChange={handleChange}
            placeholder="20100"
            disabled={isLoading}
          />
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="fiscalCode" value="Codice Fiscale" />
          </div>
          <TextInput
            id="fiscalCode"
            name="fiscalCode"
            value={formData.fiscalCode}
            onChange={handleChange}
            placeholder="****************"
            disabled={isLoading}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="address" value="Indirizzo" />
          </div>
          <TextInput
            id="address"
            name="address"
            value={formData.address}
            onChange={handleChange}
            placeholder="Via Roma 123"
            disabled={isLoading}
          />
        </div>
      </div>

      <div>
        <div className="mb-2 block">
          <Label htmlFor="notes" value="Note" />
        </div>
        <Textarea
          id="notes"
          name="notes"
          value={formData.notes}
          onChange={handleChange}
          placeholder="Inserisci eventuali note sul paziente..."
          rows={3}
          disabled={isLoading}
        />
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          color="light"
          onClick={() => navigate('/patients')}
          disabled={isLoading}
        >
          Annulla
        </Button>
        <Button
          type="submit"
          color="primary"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Spinner size="sm" className="mr-2" />
              {isEdit ? 'Aggiornamento...' : 'Salvataggio...'}
            </>
          ) : (
            isEdit ? 'Aggiorna Paziente' : 'Salva Paziente'
          )}
        </Button>
      </div>
    </form>
  );
};

export default PatientForm;
