/**
 * Servizio per la gestione dei pazienti nel database
 * 
 * Questo servizio fornisce tutte le operazioni CRUD per i pazienti,
 * incluse le relazioni con appuntamenti, fatture e file.
 * 
 * Operazioni supportate:
 * - Creazione, lettura, aggiornamento, eliminazione pazienti
 * - Ricerca e filtri avanzati
 * - Gestione relazioni con altre entità
 * - Validazioni business logic
 */

import { IPatient, ICreatePatient } from '../types/patients/IPatient';
import ApiService from './ApiService';

/**
 * Interfaccia per i dati di creazione paziente
 */
export interface CreatePatientData {
  firstName: string;
  lastName: string;
  email?: string | null;
  phone?: string | null;
  dateOfBirth?: Date | null;
  address?: string | null;
  city?: string | null;
  postalCode?: string | null;
  fiscalCode?: string | null;
  notes?: string | null;
}

/**
 * Interfaccia per i dati di aggiornamento paziente
 */
export interface UpdatePatientData extends Partial<CreatePatientData> {}

/**
 * Interfaccia per i filtri di ricerca pazienti
 */
export interface PatientFilters {
  search?: string; // Ricerca per nome, cognome, email
  city?: string;
  dateOfBirthFrom?: Date;
  dateOfBirthTo?: Date;
}

/**
 * Servizio per la gestione dei pazienti
 * Ora utilizza ApiService per le chiamate al backend
 */
class PatientService {
  /**
   * Crea un nuovo paziente
   * @param data Dati del paziente da creare
   * @returns Paziente creato
   */
  async createPatient(data: CreatePatientData): Promise<IPatient> {
    try {
      const patient = await ApiService.createPatient({
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        dateOfBirth: data.dateOfBirth,
        address: data.address,
        city: data.city,
        postalCode: data.postalCode,
        fiscalCode: data.fiscalCode,
        notes: data.notes,
      });

      console.log('✅ Paziente creato con successo:', patient.id);
      return patient;
    } catch (error: any) {
      console.error('❌ Errore creazione paziente:', error);
      throw new Error(error.message || 'Errore durante la creazione del paziente');
    }
  }

  /**
   * Ottiene tutti i pazienti con filtri opzionali
   * @param filters Filtri di ricerca
   * @param page Numero pagina (default: 1)
   * @param limit Numero elementi per pagina (default: 20)
   * @returns Lista pazienti paginata
   */
  async getPatients(
    filters: PatientFilters = {},
    page: number = 1,
    limit: number = 20
  ): Promise<{ patients: IPatient[]; total: number; pages: number }> {
    try {
      const result = await ApiService.getPatients(filters, page, limit);
      console.log(`✅ Recuperati ${result.patients.length} pazienti (pagina ${page}/${result.pages})`);
      return result;
    } catch (error: any) {
      console.error('❌ Errore recupero pazienti:', error);
      throw new Error(error.message || 'Errore durante il recupero dei pazienti');
    }
  }

  /**
   * Ottiene un paziente per ID
   * @param id ID del paziente
   * @param includeRelations Se includere le relazioni (default: false) - per ora non implementato
   * @returns Paziente trovato o null
   */
  async getPatientById(id: string, includeRelations: boolean = false): Promise<IPatient | null> {
    try {
      const patient = await ApiService.getPatientById(id);

      if (patient) {
        console.log('✅ Paziente trovato:', patient.id);
      } else {
        console.log('⚠️ Paziente non trovato:', id);
      }

      return patient;
    } catch (error: any) {
      console.error('❌ Errore recupero paziente per ID:', error);
      throw new Error(error.message || 'Errore durante il recupero del paziente');
    }
  }

  /**
   * Aggiorna un paziente
   * @param id ID del paziente da aggiornare
   * @param data Dati da aggiornare
   * @returns Paziente aggiornato
   */
  async updatePatient(id: string, data: UpdatePatientData): Promise<IPatient> {
    try {
      const patient = await ApiService.updatePatient(id, data);
      console.log('✅ Paziente aggiornato con successo:', patient.id);
      return patient;
    } catch (error: any) {
      console.error('❌ Errore aggiornamento paziente:', error);
      throw new Error(error.message || 'Errore durante l\'aggiornamento del paziente');
    }
  }

  /**
   * Elimina un paziente
   * @param id ID del paziente da eliminare
   * @returns True se eliminato con successo
   */
  async deletePatient(id: string): Promise<boolean> {
    try {
      const success = await ApiService.deletePatient(id);
      console.log('✅ Paziente eliminato con successo:', id);
      return success;
    } catch (error: any) {
      console.error('❌ Errore eliminazione paziente:', error);
      throw new Error(error.message || 'Errore durante l\'eliminazione del paziente');
    }
  }

  /**
   * Cerca pazienti per nome o cognome
   * @param query Stringa di ricerca
   * @param limit Numero massimo risultati (default: 10)
   * @returns Lista pazienti trovati
   */
  async searchPatients(query: string, limit: number = 10): Promise<IPatient[]> {
    try {
      const patients = await ApiService.searchPatients(query, limit);
      console.log(`✅ Trovati ${patients.length} pazienti per query: "${query}"`);
      return patients;
    } catch (error: any) {
      console.error('❌ Errore ricerca pazienti:', error);
      throw new Error(error.message || 'Errore durante la ricerca dei pazienti');
    }
  }
}

// Esporta l'istanza del servizio
export const patientService = new PatientService();
export default patientService;
