import { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { Spin<PERSON>, Alert } from 'flowbite-react';
import InvoiceForm from '../../components/billing/InvoiceForm';
import { useInvoices } from '../../hooks/useInvoices';
import { IInvoice } from '../../types/invoices/IInvoice';

const EditInvoice = () => {
  const { id } = useParams<{ id: string }>();
  const [invoice, setInvoice] = useState<IInvoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Hook per la gestione delle fatture
  const { invoices, findById } = useInvoices();

  // Carica i dati della fattura
  useEffect(() => {
    if (!id) {
      setError('ID fattura non valido');
      setLoading(false);
      return;
    }

    // Cerca la fattura nella lista caricata
    const foundInvoice = findById(id);
    
    if (foundInvoice) {
      setInvoice(foundInvoice);
      setLoading(false);
    } else if (invoices.length > 0) {
      // Se la lista è caricata ma la fattura non è trovata
      setError('Fattura non trovata');
      setLoading(false);
    }
    // Se invoices.length === 0, aspetta che vengano caricate
  }, [id, invoices, findById]);

  if (loading) {
    return (
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <Spinner size="lg" />
            <p className="mt-2">Caricamento fattura...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <Alert color="failure">
          <span className="font-medium">Errore!</span> {error}
        </Alert>
        <div className="mt-4">
          <Link 
            to="/billing" 
            className="inline-flex items-center gap-2 text-primary hover:underline"
          >
            <Icon icon="solar:arrow-left-linear" height={16} />
            Torna alla lista fatture
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-2">
            <h5 className="card-title">
              Modifica Fattura
              {invoice && (
                <span className="text-sm font-normal text-gray-500 ml-2">
                  {invoice.invoiceNumber}
                </span>
              )}
            </h5>
            <Link to="/billing" className="text-gray-500 hover:text-primary">
              <Icon icon="solar:arrow-left-linear" height={20} />
              <span className="sr-only">Torna alla lista fatture</span>
            </Link>
          </div>
        </div>

        {invoice && <InvoiceForm isEdit={true} invoiceData={invoice} />}
      </div>
    </>
  );
};

export default EditInvoice;
