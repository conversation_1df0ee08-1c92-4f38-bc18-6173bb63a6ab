import { InvoiceDatabaseService } from '../src/services/InvoiceDatabaseService';

export default async function handler(req: Request): Promise<Response> {
  if (req.method === 'POST') {
    const body = await req.json();
    const invoice = await InvoiceDatabaseService.createInvoice(body);
    return new Response(JSON.stringify(invoice), {
      status: 201,
      headers: { 'content-type': 'application/json' }
    });
  }
  const list = await InvoiceDatabaseService.getAllInvoices();
  return new Response(JSON.stringify(list), {
    status: 200,
    headers: { 'content-type': 'application/json' }
  });
}
