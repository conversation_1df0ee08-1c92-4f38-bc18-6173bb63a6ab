// mockData.ts
// Contiene i dati mock condivisi tra AppointmentApiService e PatientApiService

import { IPatient } from '../types/patients/IPatient';
import { IAppointment, AppointmentStatus } from '../types/appointments/IAppointment';

export const mockPatients: IPatient[] = [
  {
    id: 'patient_1',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+39 ***********',
    dateOfBirth: new Date('1980-05-15'),
    address: 'Via Roma 123',
    city: 'Milano',
    postalCode: '20100',
    fiscalCode: '****************',
    notes: 'Paziente regolare, controlli ogni 6 mesi',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-06-15')
  },
  {
    id: 'patient_2',
    firstName: 'Giulia',
    lastName: '<PERSON>ian<PERSON>',
    email: '<EMAIL>',
    phone: '+39 ***********',
    dateOfBirth: new Date('1992-08-22'),
    address: 'Via Verdi 456',
    city: 'Roma',
    postalCode: '00100',
    fiscalCode: '****************',
    notes: 'Trattamento ortodontico in corso',
    createdAt: new Date('2024-02-10'),
    updatedAt: new Date('2024-07-01')
  },
  {
    id: 'patient_3',
    firstName: 'Luca',
    lastName: 'Verdi',
    email: '<EMAIL>',
    phone: '+39 ***********',
    dateOfBirth: new Date('1975-12-03'),
    address: 'Corso Italia 789',
    city: 'Torino',
    postalCode: '10100',
    fiscalCode: '****************',
    notes: 'Allergia al lattice, utilizzare guanti nitrile',
    createdAt: new Date('2024-03-05'),
    updatedAt: new Date('2024-06-20')
  },
  {
    id: 'patient_4',
    firstName: 'Anna',
    lastName: 'Neri',
    email: '<EMAIL>',
    phone: '+39 ***********',
    dateOfBirth: new Date('1988-04-18'),
    address: 'Piazza Duomo 12',
    city: 'Firenze',
    postalCode: '50100',
    fiscalCode: '****************',
    notes: 'Paziente ansiosa, necessita sedazione',
    createdAt: new Date('2024-04-12'),
    updatedAt: new Date('2024-06-30')
  },
  {
    id: 'patient_5',
    firstName: 'Francesco',
    lastName: 'Blu',
    email: '<EMAIL>',
    phone: '+39 ***********',
    dateOfBirth: new Date('1965-11-30'),
    address: 'Via Nazionale 567',
    city: 'Napoli',
    postalCode: '80100',
    fiscalCode: '****************',
    notes: 'Diabetico, prestare attenzione alle infezioni',
    createdAt: new Date('2024-05-08'),
    updatedAt: new Date('2024-06-25')
  }
];

export function generateMockAppointments(): IAppointment[] {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  return [
    {
      id: 'apt_1',
      patientId: 'patient_1',
      title: 'Visita di controllo',
      description: 'Controllo semestrale e pulizia dentale',
      startTime: new Date(today.getTime() + 9 * 60 * 60 * 1000),
      endTime: new Date(today.getTime() + 10 * 60 * 60 * 1000),
      status: AppointmentStatus.CONFIRMED,
      type: 'Visita di controllo',
      notes: 'Paziente puntuale',
      createdAt: new Date('2024-06-15'),
      updatedAt: new Date('2024-06-20'),
      patient: {
        id: 'patient_1',
        firstName: 'Mario',
        lastName: 'Rossi',
        phone: '+39 ***********',
        email: '<EMAIL>'
      }
    },
    {
      id: 'apt_2',
      patientId: 'patient_2',
      title: 'Controllo ortodontico',
      description: 'Verifica progressi trattamento ortodontico',
      startTime: new Date(today.getTime() + 24 * 60 * 60 * 1000 + 14 * 60 * 60 * 1000),
      endTime: new Date(today.getTime() + 24 * 60 * 60 * 1000 + 15 * 60 * 60 * 1000),
      status: AppointmentStatus.SCHEDULED,
      type: 'Ortodonzia',
      notes: 'Portare bite notturno',
      createdAt: new Date('2024-06-20'),
      updatedAt: new Date('2024-06-25'),
      patient: {
        id: 'patient_2',
        firstName: 'Giulia',
        lastName: 'Bianchi',
        phone: '+39 ***********',
        email: '<EMAIL>'
      }
    },
    {
      id: 'apt_3',
      patientId: 'patient_3',
      title: 'Otturazione',
      description: 'Otturazione molare superiore destro',
      startTime: new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000 + 16 * 60 * 60 * 1000),
      endTime: new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000 + 17 * 60 * 60 * 1000),
      status: AppointmentStatus.SCHEDULED,
      type: 'Otturazione',
      notes: 'Utilizzare anestesia locale',
      createdAt: new Date('2024-06-25'),
      updatedAt: new Date('2024-06-30'),
      patient: {
        id: 'patient_3',
        firstName: 'Luca',
        lastName: 'Verdi',
        phone: '+39 ***********',
        email: '<EMAIL>'
      }
    },
    {
      id: 'apt_4',
      patientId: 'patient_4',
      title: 'Seduta di sbiancamento',
      description: 'Prima seduta di sbiancamento dentale',
      startTime: new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000 + 10 * 60 * 60 * 1000),
      endTime: new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000 + 11.5 * 60 * 60 * 1000),
      status: AppointmentStatus.CONFIRMED,
      type: 'Sbiancamento',
      notes: 'Paziente ansiosa, necessita rassicurazione',
      createdAt: new Date('2024-06-28'),
      updatedAt: new Date('2024-07-01'),
      patient: {
        id: 'patient_4',
        firstName: 'Anna',
        lastName: 'Neri',
        phone: '+39 ***********',
        email: '<EMAIL>'
      }
    },
    {
      id: 'apt_5',
      patientId: 'patient_5',
      title: 'Controllo post-intervento',
      description: 'Controllo dopo estrazione dente del giudizio',
      startTime: new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000 + 15 * 60 * 60 * 1000),
      endTime: new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000 + 15.5 * 60 * 60 * 1000),
      status: AppointmentStatus.SCHEDULED,
      type: 'Controllo post-operatorio',
      notes: 'Verificare guarigione e rimuovere punti',
      createdAt: new Date('2024-06-30'),
      updatedAt: new Date('2024-07-01'),
      patient: {
        id: 'patient_5',
        firstName: 'Francesco',
        lastName: 'Blu',
        phone: '+39 ***********',
        email: '<EMAIL>'
      }
    }
  ];
}
