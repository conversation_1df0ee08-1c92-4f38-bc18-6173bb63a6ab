{"framework": "vite", "buildCommand": "npm run build", "outputDirectory": "dist", "functions": {"api/**/*.ts": {"runtime": "@vercel/node@3"}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}, {"source": "/((?!api/).*)", "destination": "/"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}