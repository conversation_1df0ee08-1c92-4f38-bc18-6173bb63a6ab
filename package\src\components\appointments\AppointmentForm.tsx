import { useState, useEffect } from 'react';
import { Button, Label, TextInput, Select, Textarea, Alert, Spinner } from 'flowbite-react';
import { useNavigate } from 'react-router-dom';
import { useAppointments } from '../../hooks/useAppointments';
import { usePatients } from '../../hooks/usePatients';
import { 
  IAppointment, 
  ICreateAppointment, 
  AppointmentStatus,
  TREATMENT_TYPES,
  getStatusLabel
} from '../../types/appointments/IAppointment';

interface AppointmentFormProps {
  isEdit?: boolean;
  appointmentData?: IAppointment;
}

const AppointmentForm = ({ isEdit = false, appointmentData }: AppointmentFormProps) => {
  const navigate = useNavigate();
  
  // Hook per la gestione degli appuntamenti e pazienti
  const { createAppointment, updateAppointment, isLoading, error, clearError } = useAppointments({});
  const { patients } = usePatients();

  const [formData, setFormData] = useState({
    patientId: appointmentData?.patientId || '',
    title: appointmentData?.title || '',
    description: appointmentData?.description || '',
    date: appointmentData?.startTime ? 
      appointmentData.startTime.toISOString().split('T')[0] : '',
    startTime: appointmentData?.startTime ? 
      appointmentData.startTime.toTimeString().slice(0, 5) : '',
    endTime: appointmentData?.endTime ? 
      appointmentData.endTime.toTimeString().slice(0, 5) : '',
    status: appointmentData?.status || AppointmentStatus.SCHEDULED,
    type: appointmentData?.type || '',
    notes: appointmentData?.notes || ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Auto-calcola l'ora di fine se viene cambiata l'ora di inizio
    if (name === 'startTime' && value) {
      const [hours, minutes] = value.split(':').map(Number);
      const startDate = new Date();
      startDate.setHours(hours, minutes, 0, 0);
      // Aggiungi 30 minuti di default
      const endDate = new Date(startDate.getTime() + 30 * 60000);
      const endTimeString = endDate.toTimeString().slice(0, 5);
      setFormData(prev => ({
        ...prev,
        endTime: endTimeString
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => { // Removed extra closing brace
    e.preventDefault();
    clearError(); // Pulisce eventuali errori precedenti

    try {
      // Validazione base
      if (!formData.patientId || !formData.title || !formData.date || !formData.startTime || !formData.endTime) {
        alert('Tutti i campi obbligatori devono essere compilati');
        return;
      }

      // Crea oggetti Date per startTime e endTime
      const startDateTime = new Date(`${formData.date}T${formData.startTime}:00`);
      const endDateTime = new Date(`${formData.date}T${formData.endTime}:00`);

      // Validazione orari
      if (endDateTime <= startDateTime) {
        alert("L'ora di fine deve essere successiva all'ora di inizio");
        return;
      }

      // Prepara i dati per il salvataggio
      const appointmentDataToSave: ICreateAppointment = {
        patientId: formData.patientId,
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        startTime: startDateTime,
        endTime: endDateTime,
        status: formData.status,
        type: formData.type.trim() || undefined,
        notes: formData.notes.trim() || undefined,
      };

      // DEBUG: logga i dati che si stanno inviando
      console.log('[DEBUG] Dati inviati per creazione appuntamento:', appointmentDataToSave);

      let result;
      if (isEdit && appointmentData?.id) {
        // Modalità modifica
        result = await updateAppointment(appointmentData.id, appointmentDataToSave);
      } else {
        // Modalità creazione
        result = await createAppointment(appointmentDataToSave);
      }

      // DEBUG: logga il risultato della creazione
      console.log('[DEBUG] Risultato creazione/modifica appuntamento:', result);

      // CONTROLLO: se result è null o undefined, logga lo stato degli appuntamenti
      if (!result) {
        alert(`Errore durante ${isEdit ? "l'aggiornamento" : "la creazione"} dell'appuntamento. Guarda la console per dettagli.`);
        console.error(`❌ Errore durante ${isEdit ? "l'aggiornamento" : "la creazione"} dell'appuntamento`);
        // Log extra per debug
        console.error('[DEBUG] Stato appointments:', result);
        // Prova a forzare un reload della pagina degli appuntamenti
        // navigate(0); // decommenta se vuoi forzare reload
        return;
      }

      // Se tutto ok
      alert(`Appuntamento ${isEdit ? 'aggiornato' : 'creato'} con successo!`);
      // Reindirizza alla lista appuntamenti dopo il salvataggio
      navigate('/appointments');
    } catch (error) {
      alert('Errore inatteso nel form appuntamento. Guarda la console per dettagli.');
      console.error('Errore nel form:', error);
    }
  };

  return (
    <form className="space-y-4" onSubmit={handleSubmit}>
      {/* Gestione errori */}
      {error && (
        <Alert color="failure" onDismiss={clearError}>
          <span className="font-medium">Errore!</span> {error}
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="patientId" value="Paziente *" />
          </div>
          <Select
            id="patientId"
            name="patientId"
            value={formData.patientId}
            onChange={handleChange}
            required
            disabled={isLoading}
          >
            <option value="">Seleziona un paziente</option>
            {patients.map(patient => (
              <option key={patient.id} value={patient.id}>
                {patient.firstName} {patient.lastName}
              </option>
            ))}
          </Select>
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="title" value="Trattamento *" />
          </div>
          <Select
            id="title"
            name="title"
            value={formData.title}
            onChange={handleChange}
            required
            disabled={isLoading}
          >
            <option value="">Seleziona un trattamento</option>
            {TREATMENT_TYPES.map(treatment => (
              <option key={treatment} value={treatment}>
                {treatment}
              </option>
            ))}
          </Select>
        </div>
      </div>

      <div>
        <div className="mb-2 block">
          <Label htmlFor="description" value="Descrizione" />
        </div>
        <TextInput
          id="description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          placeholder="Descrizione dettagliata del trattamento..."
          disabled={isLoading}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="date" value="Data *" />
          </div>
          <TextInput
            id="date"
            name="date"
            type="date"
            value={formData.date}
            onChange={handleChange}
            required
            disabled={isLoading}
          />
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="startTime" value="Ora inizio *" />
          </div>
          <TextInput
            id="startTime"
            name="startTime"
            type="time"
            value={formData.startTime}
            onChange={handleChange}
            required
            disabled={isLoading}
          />
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="endTime" value="Ora fine *" />
          </div>
          <TextInput
            id="endTime"
            name="endTime"
            type="time"
            value={formData.endTime}
            onChange={handleChange}
            required
            disabled={isLoading}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="status" value="Stato" />
          </div>
          <Select
            id="status"
            name="status"
            value={formData.status}
            onChange={handleChange}
            disabled={isLoading}
          >
            {Object.values(AppointmentStatus).map(status => (
              <option key={status} value={status}>
                {getStatusLabel(status)}
              </option>
            ))}
          </Select>
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="type" value="Categoria" />
          </div>
          <TextInput
            id="type"
            name="type"
            value={formData.type}
            onChange={handleChange}
            placeholder="Es. Odontoiatria generale"
            disabled={isLoading}
          />
        </div>
      </div>

      <div>
        <div className="mb-2 block">
          <Label htmlFor="notes" value="Note" />
        </div>
        <Textarea
          id="notes"
          name="notes"
          value={formData.notes}
          onChange={handleChange}
          placeholder="Note aggiuntive per l'appuntamento..."
          rows={3}
          disabled={isLoading}
        />
      </div>

      <div className="flex justify-end space-x-3">
        <Button 
          color="light" 
          onClick={() => navigate('/appointments')}
          disabled={isLoading}
        >
          Annulla
        </Button>
        <Button 
          type="submit" 
          color="primary"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Spinner size="sm" className="mr-2" />
              {isEdit ? 'Aggiornamento...' : 'Salvataggio...'}
            </>
          ) : (
            isEdit ? 'Aggiorna Appuntamento' : 'Salva Appuntamento'
          )}
        </Button>
      </div>
    </form>
  );
};

export default AppointmentForm;
