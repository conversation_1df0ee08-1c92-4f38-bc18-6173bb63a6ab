/**
 * Servizio API per la gestione dei pazienti
 * 
 * Questo servizio fornisce un'interfaccia unificata per le operazioni
 * sui pazienti, gestendo automaticamente se utilizzare il database reale
 * o i dati mock in base all'ambiente.
 */

import { IPatient, ICreatePatient } from '../types/patients/IPatient';
import { mockPatients } from './mockData';

export class PatientApiService {
  /**
   * URL base per le API
   */
  private static getApiBaseUrl(): string {
    return process.env.VITE_API_URL || 'http://localhost:3001';
  }

  /**
   * Simula latenza di rete per mock data
   */
  private static async simulateNetworkDelay(ms: number = 500): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Effettua una chiamata HTTP alle API
   */
  private static async apiCall<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.getApiBaseUrl()}${endpoint}`;

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Ottiene tutti i pazienti
   */
  static async getAllPatients(): Promise<IPatient[]> {
    try {
      // Per ora utilizziamo mock data migliorati
      // TODO: Implementare API server quando risolviamo i problemi di dipendenze
      await this.simulateNetworkDelay();
      console.log('📊 Caricamento pazienti (mock data avanzati):', mockPatients.length);
      return [...mockPatients].sort((a, b) =>
        a.lastName.localeCompare(b.lastName) || a.firstName.localeCompare(b.firstName)
      );
    } catch (error) {
      console.error('❌ Errore caricamento pazienti:', error);
      throw error;
    }
  }

  /**
   * Ottiene un paziente per ID
   */
  static async getPatientById(id: string): Promise<IPatient | null> {
    try {
      await this.simulateNetworkDelay(300);
      const patient = mockPatients.find(p => p.id === id) || null;
      console.log('🔍 Paziente trovato (mock avanzato):', patient?.id);
      return patient;
    } catch (error) {
      console.error('❌ Errore recupero paziente:', error);
      throw error;
    }
  }

  /**
   * Crea un nuovo paziente
   */
  static async createPatient(patientData: ICreatePatient): Promise<IPatient> {
    try {
      await this.simulateNetworkDelay(800);

      // Validazione base
      if (!patientData.firstName || !patientData.lastName) {
        throw new Error('Nome e cognome sono obbligatori');
      }

      // Verifica duplicati email
      if (patientData.email && mockPatients.some(p => p.email === patientData.email)) {
        throw new Error('Email già esistente');
      }

      // Verifica duplicati codice fiscale
      if (patientData.fiscalCode && mockPatients.some(p => p.fiscalCode === patientData.fiscalCode)) {
        throw new Error('Codice fiscale già esistente');
      }

      const newPatient: IPatient = {
        id: `patient_${Date.now()}`,
        ...patientData,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPatients.push(newPatient);
      console.log('✅ Paziente creato (mock avanzato):', newPatient.id);
      return newPatient;
    } catch (error) {
      console.error('❌ Errore creazione paziente:', error);
      throw error;
    }
  }

  /**
   * Aggiorna un paziente esistente
   */
  static async updatePatient(id: string, patientData: Partial<ICreatePatient>): Promise<IPatient> {
    try {
      await this.simulateNetworkDelay(600);

      const patientIndex = mockPatients.findIndex(p => p.id === id);
      if (patientIndex === -1) {
        throw new Error('Paziente non trovato');
      }

      // Verifica duplicati email (escludendo il paziente corrente)
      if (patientData.email && mockPatients.some((p, index) =>
        index !== patientIndex && p.email === patientData.email)) {
        throw new Error('Email già esistente');
      }

      // Verifica duplicati codice fiscale (escludendo il paziente corrente)
      if (patientData.fiscalCode && mockPatients.some((p, index) =>
        index !== patientIndex && p.fiscalCode === patientData.fiscalCode)) {
        throw new Error('Codice fiscale già esistente');
      }

      const updatedPatient = {
        ...mockPatients[patientIndex],
        ...patientData,
        updatedAt: new Date()
      };

      mockPatients[patientIndex] = updatedPatient;
      console.log('✅ Paziente aggiornato (mock avanzato):', updatedPatient.id);
      return updatedPatient;
    } catch (error) {
      console.error('❌ Errore aggiornamento paziente:', error);
      throw error;
    }
  }

  /**
   * Elimina un paziente
   */
  static async deletePatient(id: string): Promise<boolean> {
    try {
      await this.simulateNetworkDelay(400);

      const patientIndex = mockPatients.findIndex(p => p.id === id);
      if (patientIndex === -1) {
        throw new Error('Paziente non trovato');
      }

      mockPatients.splice(patientIndex, 1);
      console.log('✅ Paziente eliminato (mock avanzato):', id);
      return true;
    } catch (error) {
      console.error('❌ Errore eliminazione paziente:', error);
      throw error;
    }
  }

  /**
   * Cerca pazienti per query
   */
  static async searchPatients(query: string): Promise<IPatient[]> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        const { PatientDatabaseService } = await import('./PatientDatabaseService');
        return await PatientDatabaseService.searchPatients(query);
      } else {
        // Ambiente browser: simula ricerca
        await this.simulateNetworkDelay(400);
        
        const searchTerm = query.toLowerCase();
        const results = mockPatients.filter(patient => 
          patient.firstName.toLowerCase().includes(searchTerm) ||
          patient.lastName.toLowerCase().includes(searchTerm) ||
          patient.email?.toLowerCase().includes(searchTerm) ||
          patient.phone?.includes(searchTerm) ||
          patient.fiscalCode?.toLowerCase().includes(searchTerm)
        );
        
        console.log(`🔍 Ricerca pazienti (mock) per "${query}":`, results.length);
        return results;
      }
    } catch (error) {
      console.error('❌ Errore ricerca pazienti:', error);
      throw error;
    }
  }

  /**
   * Conta il numero totale di pazienti
   */
  static async getPatientCount(): Promise<number> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        const { PatientDatabaseService } = await import('./PatientDatabaseService');
        return await PatientDatabaseService.getPatientCount();
      } else {
        // Ambiente browser: conta mock data
        return mockPatients.length;
      }
    } catch (error) {
      console.error('❌ Errore conteggio pazienti:', error);
      return 0;
    }
  }
}
