/**
 * Servizio API per la gestione degli appuntamenti
 *
 * Questo servizio fornisce un'interfaccia semplificata per le operazioni
 * sugli appuntamenti utilizzando le API REST.
 */

import { IAppointment, ICreateAppointment, AppointmentStatus } from '../types/appointments/IAppointment';

// Mock data solo per testing
let mockAppointments: IAppointment[] = [];
if (process.env.NODE_ENV === 'test') {
  const { generateMockAppointments } = require('./mockData');
  mockAppointments = generateMockAppointments();
}

// Type aliases per compatibilità con la richiesta
type AppointmentInput = ICreateAppointment;
type Appointment = IAppointment;

// create a new appointment
export const createAppointment = async (data: AppointmentInput) => {
  const res = await fetch('/api/appointments', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error(`HTTP ${res.status}`);
  return res.json();      // returns Appointment
};

// get all appointments
export const getAllAppointments = async () => {
  const res = await fetch('/api/appointments');
  if (!res.ok) throw new Error(`HTTP ${res.status}`);
  return res.json() as Promise<Appointment[]>;
};