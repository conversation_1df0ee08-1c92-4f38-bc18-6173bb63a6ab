/**
 * Servizio API per la gestione degli appuntamenti
 * 
 * Questo servizio fornisce un'interfaccia unificata per le operazioni
 * sugli appuntamenti, gestendo automaticamente se utilizzare il database reale
 * o i dati mock in base all'ambiente.
 */

import { IAppointment, ICreateAppointment, AppointmentStatus } from '../types/appointments/IAppointment';

import { generateMockAppointments, mockPatients } from './mockData';
let mockAppointments = generateMockAppointments();

export class AppointmentApiService {
  /**
   * Determina se siamo in ambiente server (Node.js) o browser
   */
  private static isServerEnvironment(): boolean {
    return typeof window === 'undefined';
  }

  /**
   * Simula latenza di rete
   */
  private static async simulateNetworkDelay(ms: number = 500): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Ottiene tutti gli appuntamenti
   * Utilizza sempre l'API reale per il recupero degli appuntamenti
   */
  static async getAllAppointments(): Promise<IAppointment[]> {
    try {
      const res = await fetch('/api/appointments');

      if (!res.ok) {
        throw new Error(`Errore HTTP: ${res.status}`);
      }

      const appointments = await res.json();
      console.log('📊 Appuntamenti caricati:', appointments.length);
      return appointments;
    } catch (error) {
      console.error('❌ Errore caricamento appuntamenti:', error);
      throw error;
    }
  }

  /**
   * Ottiene un appuntamento per ID
   */
  static async getAppointmentById(id: string): Promise<IAppointment | null> {
    try {
      if (this.isServerEnvironment()) {
        const res = await fetch(`http://localhost:3001/api/appointments/${id}`);
        const json = await res.json();
        if (!json.success) throw new Error(json.error || 'Errore API');
        const a = json.data;
        return {
          ...a,
          startTime: new Date(a.startTime),
          endTime: new Date(a.endTime),
          createdAt: new Date(a.createdAt),
          updatedAt: new Date(a.updatedAt)
        };
      } else {
        // Ambiente browser: usa mock data
        await this.simulateNetworkDelay(300);
        const appointment = mockAppointments.find(a => a.id === id) || null;
        console.log('🔍 Appuntamento trovato (mock):', appointment?.id);
        return appointment;
      }
    } catch (error) {
      console.error('❌ Errore recupero appuntamento:', error);
      throw error;
    }
  }

  /**
   * Crea un nuovo appuntamento
   * Utilizza sempre l'API reale per la creazione di appuntamenti
   */
  static async createAppointment(appointmentData: ICreateAppointment): Promise<IAppointment> {
    try {
      const res = await fetch('/api/appointments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(appointmentData)
      });

      if (!res.ok) {
        throw new Error(`Errore HTTP: ${res.status}`);
      }

      const result = await res.json();
      console.log('✅ Appuntamento creato:', result.id);
      return result;
    } catch (error) {
      console.error('❌ Errore creazione appuntamento:', error);
      throw error;
    }
  }

  /**
   * Aggiorna un appuntamento esistente
   */
  static async updateAppointment(id: string, appointmentData: Partial<ICreateAppointment>): Promise<IAppointment> {
    try {
      if (this.isServerEnvironment()) {
        const res = await fetch(`http://localhost:3001/api/appointments/${id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(appointmentData)
        });
        const json = await res.json();
        if (!json.success) throw new Error(json.error || 'Errore API');
        const a = json.data;
        return {
          ...a,
          startTime: new Date(a.startTime),
          endTime: new Date(a.endTime),
          createdAt: new Date(a.createdAt),
          updatedAt: new Date(a.updatedAt)
        };
      } else {
        // Ambiente browser: simula aggiornamento
        await this.simulateNetworkDelay(600);
        
        const appointmentIndex = mockAppointments.findIndex(a => a.id === id);
        if (appointmentIndex === -1) {
          throw new Error('Appuntamento non trovato');
        }
        
        const updatedAppointment = {
          ...mockAppointments[appointmentIndex],
          ...appointmentData,
          updatedAt: new Date()
        };
        
        mockAppointments[appointmentIndex] = updatedAppointment;
        mockAppointments.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
        
        console.log('✅ Appuntamento aggiornato (mock):', updatedAppointment.id);
        return updatedAppointment;
      }
    } catch (error) {
      console.error('❌ Errore aggiornamento appuntamento:', error);
      throw error;
    }
  }

  /**
   * Elimina un appuntamento
   */
  static async deleteAppointment(id: string): Promise<boolean> {
    try {
      if (this.isServerEnvironment()) {
        const res = await fetch(`http://localhost:3001/api/appointments/${id}`, {
          method: 'DELETE'
        });
        const json = await res.json();
        if (!json.success) throw new Error(json.error || 'Errore API');
        return true;
      } else {
        // Ambiente browser: simula eliminazione
        await this.simulateNetworkDelay(400);
        
        const appointmentIndex = mockAppointments.findIndex(a => a.id === id);
        if (appointmentIndex === -1) {
          throw new Error('Appuntamento non trovato');
        }
        
        mockAppointments.splice(appointmentIndex, 1);
        console.log('✅ Appuntamento eliminato (mock):', id);
        return true;
      }
    } catch (error) {
      console.error('❌ Errore eliminazione appuntamento:', error);
      throw error;
    }
  }

  /**
   * Ottiene appuntamenti per paziente
   */
  static async getAppointmentsByPatient(patientId: string): Promise<IAppointment[]> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: filtra mock data
        await this.simulateNetworkDelay(400);
        
        const results = mockAppointments
          .filter(appointment => appointment.patientId === patientId)
          .sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
        
        console.log(`🔍 Appuntamenti per paziente ${patientId} (mock):`, results.length);
        return results;
      }
    } catch (error) {
      console.error('❌ Errore ricerca appuntamenti per paziente:', error);
      throw error;
    }
  }

  /**
   * Ottiene appuntamenti per data
   */
  static async getAppointmentsByDate(date: Date): Promise<IAppointment[]> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: filtra mock data
        await this.simulateNetworkDelay(400);
        
        const targetDate = new Date(date);
        targetDate.setHours(0, 0, 0, 0);
        const nextDay = new Date(targetDate);
        nextDay.setDate(nextDay.getDate() + 1);
        
        const results = mockAppointments.filter(appointment => 
          appointment.startTime >= targetDate && appointment.startTime < nextDay
        ).sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
        
        console.log(`🔍 Appuntamenti per data ${date.toDateString()} (mock):`, results.length);
        return results;
      }
    } catch (error) {
      console.error('❌ Errore ricerca appuntamenti per data:', error);
      throw error;
    }
  }

  /**
   * Conta il numero totale di appuntamenti
   */
  static async getAppointmentCount(): Promise<number> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: conta mock data
        return mockAppointments.length;
      }
    } catch (error) {
      console.error('❌ Errore conteggio appuntamenti:', error);
      return 0;
    }
  }
}
