import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Spinner, <PERSON>ert } from "flowbite-react";
import { Icon } from "@iconify/react";
import SimpleBar from "simplebar-react";
import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { HiSearch } from "react-icons/hi";
import PageContainer from '../../components/container/PageContainer';
import { usePatients } from '../../hooks/usePatients';
import { IPatient } from '../../types/patients/IPatient';

const Patients = () => {
  const location = useLocation();
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredPatients, setFilteredPatients] = useState<IPatient[]>([]);

  // Hook per la gestione dei pazienti dal database
  const {
    patients,
    isLoading,
    error,
    searchPatients,
    clearError,
    hasPatients,
    isEmpty
  } = usePatients();

  // Aggiorna i pazienti filtrati quando cambiano i dati dal database
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredPatients(patients);
    } else {
      // Filtra localmente per ricerca rapida
      const filtered = patients.filter((patient: IPatient) => {
        const fullName = `${patient.firstName} ${patient.lastName}`.toLowerCase();
        const term = searchTerm.toLowerCase();
        return (
          fullName.includes(term) ||
          (patient.email && patient.email.toLowerCase().includes(term)) ||
          (patient.phone && patient.phone.includes(searchTerm))
        );
      });
      setFilteredPatients(filtered);
    }
  }, [patients, searchTerm]);

  // Gestisce la ricerca in tempo reale
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value;
    setSearchTerm(term);
  };

  // Verifica se siamo nella pagina di ricerca
  const isSearchPage = location.pathname === "/patients/search";

  // Imposta il focus sul campo di ricerca quando si accede alla pagina di ricerca
  useEffect(() => {
    if (isSearchPage) {
      const searchInput = document.getElementById('search-patient');
      if (searchInput) {
        searchInput.focus();
      }
    }
  }, [isSearchPage]);

  return (
    <PageContainer title="Gestione Pazienti" description="Gestisci l'elenco dei pazienti">
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <h5 className="card-title">
            Gestione Pazienti
            {hasPatients && (
              <span className="text-sm font-normal text-gray-500 ml-2">
                ({patients.length} pazienti)
              </span>
            )}
          </h5>
          <div className="flex flex-wrap gap-3 items-center">
            <div className="relative">
              <TextInput
                id="search-patient"
                type="text"
                placeholder="Cerca paziente..."
                value={searchTerm}
                onChange={handleSearch}
                className="w-full md:w-auto"
                icon={HiSearch}
                disabled={isLoading}
              />
            </div>
            <Button color="primary" className="flex items-center gap-2" as={Link} to="/patients/new">
              <Icon icon="solar:add-circle-outline" height={20} />
              Nuovo Paziente
            </Button>
          </div>
        </div>

        {/* Gestione errori */}
        {error && (
          <Alert color="failure" className="mb-4" onDismiss={clearError}>
            <span className="font-medium">Errore!</span> {error}
          </Alert>
        )}

        {/* Indicatore di caricamento */}
        {isLoading && (
          <div className="flex justify-center items-center py-8">
            <Spinner size="lg" />
            <span className="ml-2">Caricamento pazienti...</span>
          </div>
        )}
        {/* Tabella pazienti - mostrata solo se non in caricamento */}
        {!isLoading && (
          <div className="overflow-x-auto">
            <Table hoverable className="table-auto w-full">
              <Table.Head>
                <Table.HeadCell className="p-4">Nome</Table.HeadCell>
                <Table.HeadCell className="p-4 hidden sm:table-cell">Telefono</Table.HeadCell>
                <Table.HeadCell className="p-4 hidden md:table-cell">Email</Table.HeadCell>
                <Table.HeadCell className="p-4 hidden lg:table-cell">Città</Table.HeadCell>
                <Table.HeadCell className="p-4 hidden xl:table-cell">Data Nascita</Table.HeadCell>
                <Table.HeadCell className="p-4">Stato</Table.HeadCell>
                <Table.HeadCell className="p-4">Azioni</Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y divide-border dark:divide-darkborder">
                {filteredPatients.length > 0 ? (
                  filteredPatients.map((patient) => (
                  <Table.Row key={patient.id}>
                    <Table.Cell className="p-4">
                      <div className="flex gap-2 items-center">
                        <div>
                          <h6 className="text-sm font-medium">
                            {patient.firstName} {patient.lastName}
                          </h6>
                          {patient.fiscalCode && (
                            <p className="text-xs text-gray-500">CF: {patient.fiscalCode}</p>
                          )}
                        </div>
                      </div>
                    </Table.Cell>
                    <Table.Cell className="p-4 hidden sm:table-cell">
                      <p className="text-sm">{patient.phone || '-'}</p>
                    </Table.Cell>
                    <Table.Cell className="p-4 hidden md:table-cell">
                      <p className="text-sm">{patient.email || '-'}</p>
                    </Table.Cell>
                    <Table.Cell className="p-4 hidden lg:table-cell">
                      <p className="text-sm">{patient.city || '-'}</p>
                    </Table.Cell>
                    <Table.Cell className="p-4 hidden xl:table-cell">
                      <p className="text-sm">
                        {patient.dateOfBirth
                          ? new Date(patient.dateOfBirth).toLocaleDateString('it-IT')
                          : '-'
                        }
                      </p>
                    </Table.Cell>
                    <Table.Cell className="p-4">
                      <Badge className="bg-lightsuccess text-success">
                        Attivo
                      </Badge>
                    </Table.Cell>
                    <Table.Cell className="p-4">
                      <div className="flex gap-2">
                        <Button color="primary" size="xs" as={Link} to={`/patients/view/${patient.id}`}>
                          <Icon icon="solar:eye-outline" height={16} />
                        </Button>
                        <Button color="secondary" size="xs" as={Link} to={`/patients/edit/${patient.id}`}>
                          <Icon icon="solar:pen-outline" height={16} />
                        </Button>
                        </div>
                      </Table.Cell>
                    </Table.Row>
                    ))
                  ) : (
                    <Table.Row>
                      <Table.Cell colSpan={7} className="text-center py-8">
                        {isEmpty ? (
                          <div className="text-center">
                            <Icon icon="solar:users-group-two-rounded-outline" height={48} className="mx-auto text-gray-400 mb-4" />
                            <p className="text-gray-500 mb-2">Nessun paziente presente</p>
                            <p className="text-sm text-gray-400">Inizia aggiungendo il primo paziente</p>
                          </div>
                        ) : (
                          <div className="text-center">
                            <Icon icon="solar:magnifer-outline" height={48} className="mx-auto text-gray-400 mb-4" />
                            <p className="text-gray-500 mb-2">Nessun paziente trovato</p>
                            <p className="text-sm text-gray-400">Prova a modificare i criteri di ricerca</p>
                          </div>
                        )}
                      </Table.Cell>
                    </Table.Row>
                  )}
                </Table.Body>
              </Table>
            </div>
          )}
      </div>
    </PageContainer>
  );
};

export default Patients;
