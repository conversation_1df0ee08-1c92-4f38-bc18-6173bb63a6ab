/**
 * Script di test per verificare l'integrazione API completa
 * 
 * Questo script testa direttamente i servizi database per verificare
 * che tutte le operazioni CRUD funzionino correttamente.
 */

import { AppointmentDatabaseService } from '../src/services/AppointmentDatabaseService';
import { InvoiceDatabaseService } from '../src/services/InvoiceDatabaseService';
import { ProductDatabaseService } from '../src/services/ProductDatabaseService';
import { PatientDatabaseService } from '../src/services/PatientDatabaseService';
import { ICreateAppointment, AppointmentStatus } from '../src/types/appointments/IAppointment';
import { ICreateInvoice, InvoiceStatus } from '../src/types/invoices/IInvoice';
import { ICreatePatient } from '../src/types/patients/IPatient';

async function testDatabaseIntegration() {
  console.log('🚀 Avvio test integrazione database...\n');

  try {
    // Test 1: Verifica connessione e caricamento dati esistenti
    console.log('📊 Test 1: Caricamento dati esistenti');
    
    const patients = await PatientDatabaseService.getAllPatients();
    console.log(`✅ Pazienti caricati: ${patients.length}`);
    
    const appointments = await AppointmentDatabaseService.getAllAppointments();
    console.log(`✅ Appuntamenti caricati: ${appointments.length}`);
    
    const invoices = await InvoiceDatabaseService.getAllInvoices();
    console.log(`✅ Fatture caricate: ${invoices.length}`);
    
    const products = await ProductDatabaseService.getAllProducts();
    console.log(`✅ Prodotti caricati: ${products.length}\n`);

    // Test 2: Creazione nuovo paziente (se necessario)
    console.log('👤 Test 2: Gestione pazienti');
    let testPatient = patients.find(p => p.email === '<EMAIL>');
    
    if (!testPatient) {
      const newPatientData: ICreatePatient = {
        firstName: 'Test',
        lastName: 'API Integration',
        email: '<EMAIL>',
        phone: '+39 ***********',
        dateOfBirth: new Date('1990-01-01'),
        address: 'Via Test 123',
        city: 'Test City',
        postalCode: '12345',
        fiscalCode: '****************'
      };
      
      testPatient = await PatientDatabaseService.createPatient(newPatientData);
      console.log(`✅ Paziente test creato: ${testPatient.id}`);
    } else {
      console.log(`✅ Paziente test esistente: ${testPatient.id}`);
    }

    // Test 3: Creazione nuovo appuntamento
    console.log('\n📅 Test 3: Gestione appuntamenti');
    const appointmentData: ICreateAppointment = {
      patientId: testPatient.id,
      title: 'Test API Integration',
      description: 'Appuntamento di test per verifica API',
      startTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // Domani
      endTime: new Date(Date.now() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000), // Domani + 1 ora
      status: AppointmentStatus.SCHEDULED,
      type: 'Controllo'
    };
    
    const newAppointment = await AppointmentDatabaseService.createAppointment(appointmentData);
    console.log(`✅ Appuntamento creato: ${newAppointment.id}`);

    // Test 4: Creazione nuova fattura
    console.log('\n💰 Test 4: Gestione fatture');
    const invoiceData: ICreateInvoice = {
      patientId: testPatient.id,
      invoiceNumber: `TEST-${Date.now()}`,
      amount: 150.00,
      issueDate: new Date(),
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 giorni
      status: InvoiceStatus.PENDING,
      description: 'Fattura di test per verifica API'
    };
    
    const newInvoice = await InvoiceDatabaseService.createInvoice(invoiceData);
    console.log(`✅ Fattura creata: ${newInvoice.id}`);

    // Test 5: Creazione nuovo prodotto
    console.log('\n📦 Test 5: Gestione prodotti');
    const productData = {
      name: `Prodotto Test API ${Date.now()}`,
      category: 'Test',
      description: 'Prodotto di test per verifica API',
      quantity: 10,
      unit: 'pz',
      minQuantity: 5,
      price: 25.50,
      supplier: 'Test Supplier',
      location: 'Magazzino Test'
    };
    
    const newProduct = await ProductDatabaseService.createProduct(productData);
    console.log(`✅ Prodotto creato: ${newProduct.id}`);

    console.log('\n🎉 Tutti i test di integrazione completati con successo!');
    console.log('\n📋 Riepilogo test:');
    console.log(`- Paziente test: ${testPatient.firstName} ${testPatient.lastName} (${testPatient.id})`);
    console.log(`- Appuntamento: ${newAppointment.title} (${newAppointment.id})`);
    console.log(`- Fattura: ${newInvoice.invoiceNumber} - €${newInvoice.amount} (${newInvoice.id})`);
    console.log(`- Prodotto: ${newProduct.name} (${newProduct.id})`);

  } catch (error) {
    console.error('❌ Errore durante i test di integrazione:', error);
    throw error;
  }
}

// Esegui i test
testDatabaseIntegration()
  .then(() => {
    console.log('\n✅ Script di test completato con successo');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Script di test fallito:', error);
    process.exit(1);
  });
