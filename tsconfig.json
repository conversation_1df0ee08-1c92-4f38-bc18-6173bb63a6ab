{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowJs": true, "noEmit": false, "outDir": ".vercel/output", "baseUrl": ".", "paths": {"@/*": ["package/src/*"]}}, "include": ["api/**/*", "package/src/**/*"], "exclude": ["node_modules", ".vercel", "package/node_modules"]}