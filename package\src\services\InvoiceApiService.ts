/**
 * Servizio API per la gestione delle fatture
 *
 * Questo servizio fornisce un'interfaccia semplificata per le operazioni
 * sulle fatture utilizzando le API REST.
 */

import { IInvoice, ICreateInvoice, InvoiceStatus } from '../types/invoices/IInvoice';

// Mock data solo per testing
let mockInvoices: IInvoice[] = [];
if (process.env.NODE_ENV === 'test') {
  // Mock data per l'ambiente di test
  const generateMockInvoices = (): IInvoice[] => {
    const now = new Date();

    return [
      {
        id: 'inv_1',
        patientId: 'patient_1',
        invoiceNumber: 'FAT-2024-001',
        issueDate: new Date(now.getFullYear(), now.getMonth(), 1),
        dueDate: new Date(now.getFullYear(), now.getMonth(), 31),
        amount: 150.00,
        taxAmount: 33.00,
        totalAmount: 183.00,
        status: InvoiceStatus.PAID,
        description: 'Visita di controllo e pulizia dentale',
        notes: 'Pagamento ricevuto in contanti',
        createdAt: new Date(now.getFullYear(), now.getMonth(), 1),
        updatedAt: new Date(now.getFullYear(), now.getMonth(), 5),
        patient: {
          id: 'patient_1',
          firstName: 'Mario',
          lastName: 'Rossi',
          email: '<EMAIL>',
          phone: '+39 ***********'
        }
      }
    ];
  };
  mockInvoices = generateMockInvoices();
}

// Type aliases per compatibilità con la richiesta
type InvoiceInput = ICreateInvoice;
type Invoice = IInvoice;

export class InvoiceApiService {
  /**
   * Determina se siamo in ambiente server (Node.js) o browser
   */
  private static isServerEnvironment(): boolean {
    return typeof window === 'undefined';
  }

  /**
   * Simula latenza di rete
   */
  private static async simulateNetworkDelay(ms: number = 500): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Ottiene tutte le fatture
   * Utilizza sempre l'API reale per il recupero delle fatture
   */
  static async getAllInvoices(): Promise<IInvoice[]> {
    try {
      const res = await fetch('/api/invoices');

      if (!res.ok) {
        throw new Error(`Errore HTTP: ${res.status}`);
      }

      const invoices = await res.json();
      console.log('📊 Fatture caricate:', invoices.length);
      return invoices;
    } catch (error) {
      console.error('❌ Errore caricamento fatture:', error);
      throw error;
    }
  }

  /**
   * Ottiene una fattura per ID
   */
  static async getInvoiceById(id: string): Promise<IInvoice | null> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: usa mock data
        await this.simulateNetworkDelay(300);
        const invoice = mockInvoices.find(i => i.id === id) || null;
        console.log('🔍 Fattura trovata (mock):', invoice?.id);
        return invoice;
      }
    } catch (error) {
      console.error('❌ Errore recupero fattura:', error);
      throw error;
    }
  }

  /**
   * Crea una nuova fattura
   * Utilizza sempre l'API reale per la creazione di fatture
   */
  static async createInvoice(invoiceData: ICreateInvoice): Promise<IInvoice> {
    try {
      const res = await fetch('/api/invoices', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invoiceData)
      });

      if (!res.ok) {
        throw new Error(`Errore HTTP: ${res.status}`);
      }

      const result = await res.json();
      console.log('✅ Fattura creata:', result.id);
      return result;
    } catch (error) {
      console.error('❌ Errore creazione fattura:', error);
      throw error;
    }
  }

  /**
   * Aggiorna una fattura esistente
   */
  static async updateInvoice(id: string, invoiceData: Partial<ICreateInvoice>): Promise<IInvoice> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: simula aggiornamento
        await this.simulateNetworkDelay(600);
        
        const invoiceIndex = mockInvoices.findIndex(i => i.id === id);
        if (invoiceIndex === -1) {
          throw new Error('Fattura non trovata');
        }
        
        // Ricalcola totali se l'importo è cambiato
        let taxAmount = invoiceData.taxAmount;
        let totalAmount = invoiceData.amount;
        
        if (invoiceData.amount && !invoiceData.taxAmount) {
          taxAmount = invoiceData.amount * 0.22;
        }
        
        if (invoiceData.amount && taxAmount) {
          totalAmount = invoiceData.amount + taxAmount;
        }
        
        const updatedInvoice = {
          ...mockInvoices[invoiceIndex],
          ...invoiceData,
          ...(taxAmount && { taxAmount }),
          ...(totalAmount && { totalAmount }),
          updatedAt: new Date()
        };
        
        mockInvoices[invoiceIndex] = updatedInvoice;
        mockInvoices.sort((a, b) => b.issueDate.getTime() - a.issueDate.getTime());
        
        console.log('✅ Fattura aggiornata (mock):', updatedInvoice.id);
        return updatedInvoice;
      }
    } catch (error) {
      console.error('❌ Errore aggiornamento fattura:', error);
      throw error;
    }
  }

  /**
   * Elimina una fattura
   */
  static async deleteInvoice(id: string): Promise<boolean> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: simula eliminazione
        await this.simulateNetworkDelay(400);
        
        const invoiceIndex = mockInvoices.findIndex(i => i.id === id);
        if (invoiceIndex === -1) {
          throw new Error('Fattura non trovata');
        }
        
        mockInvoices.splice(invoiceIndex, 1);
        console.log('✅ Fattura eliminata (mock):', id);
        return true;
      }
    } catch (error) {
      console.error('❌ Errore eliminazione fattura:', error);
      throw error;
    }
  }

  /**
   * Ottiene fatture per paziente
   */
  static async getInvoicesByPatient(patientId: string): Promise<IInvoice[]> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: filtra mock data
        await this.simulateNetworkDelay(400);
        
        const results = mockInvoices
          .filter(invoice => invoice.patientId === patientId)
          .sort((a, b) => b.issueDate.getTime() - a.issueDate.getTime());
        
        console.log(`🔍 Fatture per paziente ${patientId} (mock):`, results.length);
        return results;
      }
    } catch (error) {
      console.error('❌ Errore ricerca fatture per paziente:', error);
      throw error;
    }
  }

  /**
   * Conta il numero totale di fatture
   */
  static async getInvoiceCount(): Promise<number> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: conta mock data
        return mockInvoices.length;
      }
    } catch (error) {
      console.error('❌ Errore conteggio fatture:', error);
      return 0;
    }
  }
}
