/**
 * Servizio API per la gestione delle fatture
 *
 * Questo servizio fornisce un'interfaccia semplificata per le operazioni
 * sulle fatture utilizzando le API REST.
 */

import { IInvoice, ICreateInvoice, InvoiceStatus } from '../types/invoices/IInvoice';

// Mock data solo per testing
let mockInvoices: IInvoice[] = [];
if (process.env.NODE_ENV === 'test') {
  // Mock data per l'ambiente di test
  const generateMockInvoices = (): IInvoice[] => {
    const now = new Date();

    return [
      {
        id: 'inv_1',
        patientId: 'patient_1',
        invoiceNumber: 'FAT-2024-001',
        issueDate: new Date(now.getFullYear(), now.getMonth(), 1),
        dueDate: new Date(now.getFullYear(), now.getMonth(), 31),
        amount: 150.00,
        taxAmount: 33.00,
        totalAmount: 183.00,
        status: InvoiceStatus.PAID,
        description: 'Visita di controllo e pulizia dentale',
        notes: 'Pagamento ricevuto in contanti',
        createdAt: new Date(now.getFullYear(), now.getMonth(), 1),
        updatedAt: new Date(now.getFullYear(), now.getMonth(), 5),
        patient: {
          id: 'patient_1',
          firstName: 'Mario',
          lastName: 'Rossi',
          email: '<EMAIL>',
          phone: '+39 ***********'
        }
      }
    ];
  };
  mockInvoices = generateMockInvoices();
}

// Type aliases per compatibilità con la richiesta
type InvoiceInput = ICreateInvoice;
type Invoice = IInvoice;

// create a new invoice
export const createInvoice = async (data: InvoiceInput) => {
  const res = await fetch('/api/invoices', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error(`HTTP ${res.status}`);
  return res.json() as Promise<Invoice>;
};

// get all invoices
export const getAllInvoices = async () => {
  const res = await fetch('/api/invoices');
  if (!res.ok) throw new Error(`HTTP ${res.status}`);
  return res.json() as Promise<Invoice[]>;
};
