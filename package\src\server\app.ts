/**
 * Server Express per le API routes
 * 
 * Questo server gestisce tutte le operazioni database server-side
 * e fornisce API REST per il frontend React.
 */

import express from 'express';
import cors from 'cors';
import { prismaService } from '../services/PrismaService';
import * as invoiceApi from '../api/invoices';
import * as productApi from '../api/products';

const app = express();
const PORT = process.env.API_PORT || 3001;

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5174',
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', async (req, res) => {
  try {
    const isHealthy = await prismaService.healthCheck();
    res.json({
      status: 'ok',
      database: isHealthy ? 'connected' : 'disconnected',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      database: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

// API Pazienti
app.get('/api/patients', asyncHandler(async (req, res) => {
  const prisma = prismaService.getClient();
  const patients = await prisma.patient.findMany({
    orderBy: [
      { lastName: 'asc' },
      { firstName: 'asc' }
    ]
  });
  const formattedPatients = patients.map(patient => ({
    id: patient.id,
    firstName: patient.firstName,
    lastName: patient.lastName,
    email: patient.email,
    phone: patient.phone,
    dateOfBirth: patient.dateOfBirth,
    address: patient.address,
    city: patient.city,
    postalCode: patient.postalCode,
    fiscalCode: patient.fiscalCode,
    notes: patient.notes,
    createdAt: patient.createdAt,
    updatedAt: patient.updatedAt
  }));
  res.json({
    success: true,
    data: formattedPatients,
    count: formattedPatients.length
  });
}));

app.post('/api/patients', asyncHandler(async (req, res) => {
  const patientData = req.body;
  const prisma = prismaService.getClient();
  if (!patientData.firstName || !patientData.lastName) {
    return res.status(400).json({
      success: false,
      error: 'Nome e cognome sono obbligatori'
    });
  }
  const patient = await prisma.patient.create({
    data: {
      firstName: patientData.firstName,
      lastName: patientData.lastName,
      email: patientData.email,
      phone: patientData.phone,
      dateOfBirth: patientData.dateOfBirth ? new Date(patientData.dateOfBirth) : null,
      address: patientData.address,
      city: patientData.city,
      postalCode: patientData.postalCode,
      fiscalCode: patientData.fiscalCode,
      notes: patientData.notes
    }
  });
  const formattedPatient = {
    id: patient.id,
    firstName: patient.firstName,
    lastName: patient.lastName,
    email: patient.email,
    phone: patient.phone,
    dateOfBirth: patient.dateOfBirth,
    address: patient.address,
    city: patient.city,
    postalCode: patient.postalCode,
    fiscalCode: patient.fiscalCode,
    notes: patient.notes,
    createdAt: patient.createdAt,
    updatedAt: patient.updatedAt
  };
  res.status(201).json({
    success: true,
    data: formattedPatient,
    message: 'Paziente creato con successo'
  });
}));

// Wrapper per adattare async handler a Express
function asyncHandler(
  fn: (req: express.Request, res: express.Response, next: express.NextFunction) => Promise<any>
) {
  return function (req: express.Request, res: express.Response, next: express.NextFunction) {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

// API Fatture
app.get('/api/invoices', asyncHandler(invoiceApi.getAllInvoices));
app.get('/api/invoices/:id', asyncHandler(invoiceApi.getInvoiceById));
app.post('/api/invoices', asyncHandler(invoiceApi.createInvoice));
app.put('/api/invoices/:id', asyncHandler(invoiceApi.updateInvoice));
app.delete('/api/invoices/:id', asyncHandler(invoiceApi.deleteInvoice));

// API Prodotti (Inventario)
app.get('/api/products', asyncHandler(productApi.getAllProducts));
app.get('/api/products/:id', asyncHandler(productApi.getProductById));
app.post('/api/products', asyncHandler(productApi.createProduct));
app.put('/api/products/:id', asyncHandler(productApi.updateProduct));
app.delete('/api/products/:id', asyncHandler(productApi.deleteProduct));

// Error handling middleware
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('❌ Errore server:', error);
  res.status(500).json({
    success: false,
    error: 'Errore interno del server',
    details: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint non trovato',
    path: req.originalUrl
  });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🔄 Ricevuto SIGTERM, chiusura graceful...');
  await prismaService.disconnect();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🔄 Ricevuto SIGINT, chiusura graceful...');
  await prismaService.disconnect();
  process.exit(0);
});

export default app;

// Avvia sempre il server (compatibile con ES module)
app.listen(PORT, () => {
  console.log(`🚀 Server API in esecuzione su http://localhost:${PORT}`);
  console.log(`📊 Health check disponibile su http://localhost:${PORT}/health`);
  console.log(`👥 API Pazienti disponibile su http://localhost:${PORT}/api/patients`);
});
