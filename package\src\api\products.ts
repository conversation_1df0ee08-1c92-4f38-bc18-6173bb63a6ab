import { Request, Response, NextFunction } from 'express';
import { ProductDatabaseService } from '../services/ProductDatabaseService';

export async function getAllProducts(req: Request, res: Response, next: NextFunction) {
  try {
    const products = await ProductDatabaseService.getAllProducts();
    res.json(products);
  } catch (err) {
    next(err);
  }
}

export async function getProductById(req: Request, res: Response, next: NextFunction) {
  try {
    const id = Number(req.params.id);
    if (isNaN(id)) return res.status(400).json({ error: 'ID non valido' });
    const product = await ProductDatabaseService.getProductById(id);
    if (!product) return res.status(404).json({ error: 'Prodotto non trovato' });
    res.json(product);
  } catch (err) {
    next(err);
  }
}

export async function createProduct(req: Request, res: Response, next: NextFunction) {
  try {
    const product = await ProductDatabaseService.createProduct(req.body);
    res.status(201).json(product);
  } catch (err) {
    next(err);
  }
}

export async function updateProduct(req: Request, res: Response, next: NextFunction) {
  try {
    const id = Number(req.params.id);
    if (isNaN(id)) return res.status(400).json({ error: 'ID non valido' });
    const product = await ProductDatabaseService.updateProduct(id, req.body);
    res.json(product);
  } catch (err) {
    next(err);
  }
}

export async function deleteProduct(req: Request, res: Response, next: NextFunction) {
  try {
    const id = Number(req.params.id);
    if (isNaN(id)) return res.status(400).json({ error: 'ID non valido' });
    await ProductDatabaseService.deleteProduct(id);
    res.status(204).end();
  } catch (err) {
    next(err);
  }
}
