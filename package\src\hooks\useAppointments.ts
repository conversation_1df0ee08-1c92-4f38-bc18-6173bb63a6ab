/**
 * Hook personalizzato per la gestione degli appuntamenti
 * Fornisce funzionalità CRUD complete con gestione stato e errori
 * Utilizza il nuovo servizio API che gestisce automaticamente l'ambiente
 */

import { useState, useEffect, useCallback } from 'react';
import {
  IAppointment,
  ICreateAppointment,
  IUpdateAppointment,
  IAppointmentFilters,
  IAppointmentStats,
  AppointmentStatus,
  getStatusLabel,
  getStatusColor,
  formatAppointmentDateTime,
  formatAppointmentDate,
  formatAppointmentTime
} from '../types/appointments/IAppointment';
import { createAppointment, getAllAppointments } from '../services/AppointmentApiService';

// Interfaccia per le opzioni dell'hook

interface UseAppointmentsOptions {
  autoLoad?: boolean;
  filters?: IAppointmentFilters;
}

export const useAppointments = (options: UseAppointmentsOptions = {}) => {
  const { autoLoad = true, filters } = options;

  // Stato locale
  const [appointments, setAppointments] = useState<IAppointment[]>([]);
  const [filteredAppointments, setFilteredAppointments] = useState<IAppointment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Carica tutti gli appuntamenti
  const loadAppointments = useCallback(async (): Promise<IAppointment[]> => {
    setIsLoading(true);
    setError(null);

    try {
      const appointmentsData = await getAllAppointments();
      setAppointments(appointmentsData);
      console.log('✅ Appuntamenti caricati:', appointmentsData.length);
      return appointmentsData;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nel caricamento degli appuntamenti';
      setError(errorMessage);
      console.error('❌ Errore caricamento appuntamenti:', err);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Crea un nuovo appuntamento
  const createAppointmentHandler = useCallback(async (appointmentData: ICreateAppointment): Promise<IAppointment | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const newAppointment = await createAppointment(appointmentData);

      // Aggiorna lo stato locale
      setAppointments(prev => [...prev, newAppointment].sort((a, b) =>
        a.startTime.getTime() - b.startTime.getTime()
      ));

      console.log('✅ Appuntamento creato:', newAppointment.id);
      return newAppointment;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nella creazione dell\'appuntamento';
      setError(errorMessage);
      console.error('❌ Errore creazione appuntamento:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Aggiorna un appuntamento esistente
  const updateAppointment = useCallback(async (id: string, appointmentData: Partial<ICreateAppointment>): Promise<IAppointment | null> => {
    setIsLoading(true);
    setError(null);

    try {
      // TODO: Implementare updateAppointment API
      throw new Error('updateAppointment non ancora implementato');

      // Aggiorna lo stato locale
      setAppointments(prev => prev.map(appointment =>
        appointment.id === id ? updatedAppointment : appointment
      ).sort((a, b) => a.startTime.getTime() - b.startTime.getTime()));

      console.log('✅ Appuntamento aggiornato:', updatedAppointment.id);
      return updatedAppointment;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nell\'aggiornamento dell\'appuntamento';
      setError(errorMessage);
      console.error('❌ Errore aggiornamento appuntamento:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Elimina un appuntamento
  const deleteAppointment = useCallback(async (id: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      // TODO: Implementare deleteAppointment API
      throw new Error('deleteAppointment non ancora implementato');

      if (result) {
        // Rimuovi dalla lista locale
        setAppointments(prev => prev.filter(appointment => appointment.id !== id));
        console.log('✅ Appuntamento eliminato:', id);
      }

      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nell\'eliminazione dell\'appuntamento';
      setError(errorMessage);
      console.error('❌ Errore eliminazione appuntamento:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Pulisce l'errore corrente
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Caricamento iniziale
  useEffect(() => {
    if (autoLoad && appointments.length === 0) {
      loadAppointments();
    }
  }, [autoLoad, appointments.length, loadAppointments]);

  // Applicazione filtri
  useEffect(() => {
    let filtered = [...appointments];
    
    if (filters) {
      if (filters.patientId) {
        filtered = filtered.filter(apt => apt.patientId === filters.patientId);
      }
      
      if (filters.status) {
        filtered = filtered.filter(apt => apt.status === filters.status);
      }
      
      if (filters.type) {
        filtered = filtered.filter(apt => apt.type === filters.type);
      }
      
      if (filters.startDate) {
        filtered = filtered.filter(apt => apt.startTime >= filters.startDate!);
      }
      
      if (filters.endDate) {
        filtered = filtered.filter(apt => apt.startTime <= filters.endDate!);
      }
      
      if (filters.searchTerm) {
        const term = filters.searchTerm.toLowerCase();
        filtered = filtered.filter(apt => 
          apt.title.toLowerCase().includes(term) ||
          apt.description?.toLowerCase().includes(term) ||
          apt.patient?.firstName.toLowerCase().includes(term) ||
          apt.patient?.lastName.toLowerCase().includes(term) ||
          apt.notes?.toLowerCase().includes(term)
        );
      }
    }
    
    setFilteredAppointments(filtered);
  }, [appointments, filters]);

  // Utility functions
  const findById = useCallback((id: string): IAppointment | undefined => {
    return appointments.find(appointment => appointment.id === id);
  }, [appointments]);

  const getAppointmentsByDate = useCallback((date: Date): IAppointment[] => {
    const targetDate = new Date(date);
    targetDate.setHours(0, 0, 0, 0);
    const nextDay = new Date(targetDate);
    nextDay.setDate(nextDay.getDate() + 1);
    
    return appointments.filter(apt => 
      apt.startTime >= targetDate && apt.startTime < nextDay
    );
  }, [appointments]);

  const getAppointmentsByPatient = useCallback((patientId: string): IAppointment[] => {
    return appointments.filter(apt => apt.patientId === patientId);
  }, [appointments]);

  const getStats = useCallback((): IAppointmentStats => {
    const now = new Date();
    const today = new Date(now);
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const weekStart = new Date(today);
    weekStart.setDate(today.getDate() - today.getDay());
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 7);
    
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 1);
    
    return {
      total: appointments.length,
      scheduled: appointments.filter(a => a.status === AppointmentStatus.SCHEDULED).length,
      confirmed: appointments.filter(a => a.status === AppointmentStatus.CONFIRMED).length,
      inProgress: appointments.filter(a => a.status === AppointmentStatus.IN_PROGRESS).length,
      completed: appointments.filter(a => a.status === AppointmentStatus.COMPLETED).length,
      cancelled: appointments.filter(a => a.status === AppointmentStatus.CANCELLED).length,
      noShow: appointments.filter(a => a.status === AppointmentStatus.NO_SHOW).length,
      todayTotal: appointments.filter(a => a.startTime >= today && a.startTime < tomorrow).length,
      weekTotal: appointments.filter(a => a.startTime >= weekStart && a.startTime < weekEnd).length,
      monthTotal: appointments.filter(a => a.startTime >= monthStart && a.startTime < monthEnd).length,
    };
  }, [appointments]);

  const refresh = useCallback(() => {
    return loadAppointments();
  }, [loadAppointments]);

  return {
    // Dati
    appointments: filteredAppointments.length > 0 ? filteredAppointments : appointments,
    allAppointments: appointments,
    isLoading,
    error,
    
    // Azioni CRUD
    createAppointment: createAppointmentHandler,
    updateAppointment,
    deleteAppointment,
    
    // Utilità
    refresh,
    findById,
    getAppointmentsByDate,
    getAppointmentsByPatient,
    getStats,
    clearError,
    
    // Stato
    hasData: appointments.length > 0,
    isEmpty: appointments.length === 0,
    
    // Helper per UI
    getStatusLabel,
    getStatusColor,
    formatAppointmentDateTime,
    formatAppointmentDate,
    formatAppointmentTime
  };
};
