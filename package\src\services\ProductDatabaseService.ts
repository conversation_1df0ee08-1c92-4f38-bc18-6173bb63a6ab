import { prismaService } from './PrismaService';
import { IProduct } from '../types/inventory/IProduct';

export class ProductDatabaseService {
  static async getAllProducts(): Promise<IProduct[]> {
    const prisma = prismaService.getClient();
    const products = await prisma.product.findMany();
    return products.map(p => ({
      id: p.id,
      name: p.name,
      category: p.category,
      description: p.description ?? undefined,
      quantity: p.quantity,
      unit: p.unit,
      minQuantity: p.minQuantity,
      price: Number(p.price),
      supplier: p.supplier ?? undefined,
      location: p.location ?? undefined,
      notes: p.notes ?? undefined,
      lastOrder: p.lastOrder ? p.lastOrder.toISOString() : undefined
    }));
  }

  static async getProductById(id: number): Promise<IProduct | null> {
    const prisma = prismaService.getClient();
    const p = await prisma.product.findUnique({ where: { id } });
    if (!p) return null;
    return {
      id: p.id,
      name: p.name,
      category: p.category,
      description: p.description ?? undefined,
      quantity: p.quantity,
      unit: p.unit,
      minQuantity: p.minQuantity,
      price: Number(p.price),
      supplier: p.supplier ?? undefined,
      location: p.location ?? undefined,
      notes: p.notes ?? undefined,
      lastOrder: p.lastOrder ? p.lastOrder.toISOString() : undefined
    };
  }

  static async createProduct(data: Omit<IProduct, 'id'>): Promise<IProduct> {
    const prisma = prismaService.getClient();
    const p = await prisma.product.create({ data });
    return {
      id: p.id,
      name: p.name,
      category: p.category,
      description: p.description ?? undefined,
      quantity: p.quantity,
      unit: p.unit,
      minQuantity: p.minQuantity,
      price: Number(p.price),
      supplier: p.supplier ?? undefined,
      location: p.location ?? undefined,
      notes: p.notes ?? undefined,
      lastOrder: p.lastOrder ? p.lastOrder.toISOString() : undefined
    };
  }

  static async updateProduct(id: number, data: Partial<Omit<IProduct, 'id'>>): Promise<IProduct> {
    const prisma = prismaService.getClient();
    const p = await prisma.product.update({ where: { id }, data });
    return {
      id: p.id,
      name: p.name,
      category: p.category,
      description: p.description ?? undefined,
      quantity: p.quantity,
      unit: p.unit,
      minQuantity: p.minQuantity,
      price: Number(p.price),
      supplier: p.supplier ?? undefined,
      location: p.location ?? undefined,
      notes: p.notes ?? undefined,
      lastOrder: p.lastOrder ? p.lastOrder.toISOString() : undefined
    };
  }

  static async deleteProduct(id: number): Promise<boolean> {
    const prisma = prismaService.getClient();
    await prisma.product.delete({ where: { id } });
    return true;
  }
}
