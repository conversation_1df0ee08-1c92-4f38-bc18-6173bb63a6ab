import { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { Spin<PERSON>, Alert } from 'flowbite-react';
import PatientForm from '../../components/patients/PatientForm';
import { usePatients } from '../../hooks/usePatients';
import { IPatient } from '../../types/patients/IPatient';

const EditPatient = () => {
  const { id } = useParams<{ id: string }>();
  const [patient, setPatient] = useState<IPatient | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Hook per la gestione dei pazienti
  const { patients, findById } = usePatients();

  // Carica i dati del paziente
  useEffect(() => {
    if (!id) {
      setError('ID paziente non valido');
      setLoading(false);
      return;
    }

    // Cerca il paziente nella lista caricata
    const foundPatient = findById(id);

    if (foundPatient) {
      setPatient(foundPatient);
      setLoading(false);
    } else if (patients.length > 0) {
      // Se la lista è caricata ma il paziente non è trovato
      setError('Paziente non trovato');
      setLoading(false);
    }
    // Se patients.length === 0, aspetta che vengano caricati
  }, [id, patients, findById]);

  if (loading) {
    return (
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <Spinner size="lg" />
            <p className="mt-2">Caricamento paziente...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <Alert color="failure">
          <span className="font-medium">Errore!</span> {error}
        </Alert>
        <div className="mt-4">
          <Link
            to="/patients"
            className="inline-flex items-center gap-2 text-primary hover:underline"
          >
            <Icon icon="solar:arrow-left-linear" height={16} />
            Torna alla lista pazienti
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-2">
            <h5 className="card-title">
              Modifica Paziente
              {patient && (
                <span className="text-sm font-normal text-gray-500 ml-2">
                  {patient.firstName} {patient.lastName}
                </span>
              )}
            </h5>
            <Link to="/patients" className="text-gray-500 hover:text-primary">
              <Icon icon="solar:arrow-left-linear" height={20} />
              <span className="sr-only">Torna alla lista pazienti</span>
            </Link>
          </div>
        </div>

        {patient && <PatientForm isEdit={true} patientData={patient} />}
      </div>
    </>
  );
};

export default EditPatient;
